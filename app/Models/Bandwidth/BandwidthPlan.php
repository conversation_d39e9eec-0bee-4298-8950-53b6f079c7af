<?php

namespace App\Models\Bandwidth;

use App\Traits\BelongsToOrganization;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class BandwidthPlan extends Model
{
    use HasFactory, BelongsToOrganization;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'download_speed',
        'upload_speed',
        'burst_download_speed',
        'burst_upload_speed',
        'burst_time',
        'priority',
        'price',
        'active',
        'organization_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'download_speed' => 'integer',
        'upload_speed' => 'integer',
        'burst_download_speed' => 'integer',
        'burst_upload_speed' => 'integer',
        'burst_time' => 'integer',
        'priority' => 'integer',
        'price' => 'decimal:2',
        'active' => 'boolean',
    ];

    /**
     * Get the assignments for the plan.
     */
    public function assignments(): MorphMany
    {
        return $this->morphMany(BandwidthAssignment::class, 'assignable');
    }

    /**
     * Scope a query to only include active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope a query to only include plans with a minimum download speed.
     */
    public function scopeMinDownloadSpeed($query, $speed)
    {
        return $query->where('download_speed', '>=', $speed);
    }

    /**
     * Scope a query to only include plans with a minimum upload speed.
     */
    public function scopeMinUploadSpeed($query, $speed)
    {
        return $query->where('upload_speed', '>=', $speed);
    }

    /**
     * Scope a query to only include plans with a specific priority.
     */
    public function scopeWithPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Get the download speed in Mbps (already stored in Mbps).
     */
    public function getDownloadSpeedMbpsAttribute()
    {
        return $this->download_speed;
    }

    /**
     * Get the upload speed in Mbps (already stored in Mbps).
     */
    public function getUploadSpeedMbpsAttribute()
    {
        return $this->upload_speed;
    }

    /**
     * Get the burst download speed in Mbps (already stored in Mbps).
     */
    public function getBurstDownloadSpeedMbpsAttribute()
    {
        return $this->burst_download_speed;
    }

    /**
     * Get the burst upload speed in Mbps (already stored in Mbps).
     */
    public function getBurstUploadSpeedMbpsAttribute()
    {
        return $this->burst_upload_speed;
    }

    /**
     * Get the download speed in Kbps for MikroTik API.
     *
     * @deprecated Use download_speed with 'M' suffix instead (e.g., '20M' not '20480k')
     * This accessor converts Mbps to Kbps but should not be used with 'k' suffix
     * as it results in double conversion (20 Mbps -> 20480 Kbps -> '20480k' = 20,480 Kbps)
     */
    public function getDownloadSpeedKbpsAttribute()
    {
        return $this->download_speed * 1024;
    }

    /**
     * Get the upload speed in Kbps for MikroTik API.
     *
     * @deprecated Use upload_speed with 'M' suffix instead (e.g., '20M' not '20480k')
     * This accessor converts Mbps to Kbps but should not be used with 'k' suffix
     * as it results in double conversion (20 Mbps -> 20480 Kbps -> '20480k' = 20,480 Kbps)
     */
    public function getUploadSpeedKbpsAttribute()
    {
        return $this->upload_speed * 1024;
    }

    /**
     * Get the burst download speed in Kbps for MikroTik API.
     */
    public function getBurstDownloadSpeedKbpsAttribute()
    {
        return $this->burst_download_speed ? $this->burst_download_speed * 1024 : null;
    }

    /**
     * Get the burst upload speed in Kbps for MikroTik API.
     */
    public function getBurstUploadSpeedKbpsAttribute()
    {
        return $this->burst_upload_speed ? $this->burst_upload_speed * 1024 : null;
    }

    /**
     * Assign this plan to a model.
     */
    public function assignTo($model, $startsAt = null, $endsAt = null)
    {
        return $this->assignments()->create([
            'assignee_type' => get_class($model),
            'assignee_id' => $model->id,
            'starts_at' => $startsAt,
            'ends_at' => $endsAt,
            'active' => true,
        ]);
    }
}
