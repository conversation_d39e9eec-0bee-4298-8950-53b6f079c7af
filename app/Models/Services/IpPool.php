<?php

namespace App\Models\Services;

use App\Models\Network\IpAddress;
use App\Models\Network\NetworkDevice;
use App\Traits\BelongsToOrganization;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class IpPool extends Model
{
    use HasFactory, BelongsToOrganization;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'network_cidr',
        'cidr_range', // Original CIDR for IP range calculation (e.g., /27)
        'network_address',
        'subnet_mask', // Always ************* for private networks
        'gateway',
        'dns_servers',
        'device_id',
        'status',
        'mikrotik_pool_id',
        'excluded_ips',
        'interface',
        'addresses_created',
        'total_addresses',
        'available_addresses',
        'assigned_addresses',
        'creation_log',
        'organization_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'dns_servers' => 'array',
        'excluded_ips' => 'array',
        'addresses_created' => 'boolean',
        'creation_log' => 'array',
    ];

    /**
     * Get the device (router) that hosts this IP pool.
     */
    public function device(): BelongsTo
    {
        return $this->belongsTo(NetworkDevice::class, 'device_id');
    }

    /**
     * Get the static IP services that use IPs from this pool.
     */
    public function staticIpServices(): HasMany
    {
        return $this->hasMany(StaticIpService::class, 'ip_pool_id');
    }

    /**
     * Get all IP addresses in this pool.
     */
    public function ipAddresses(): HasMany
    {
        return $this->hasMany(IpAddress::class, 'ip_pool_id');
    }

    /**
     * Get available IP addresses in this pool.
     */
    public function availableIpAddresses(): HasMany
    {
        return $this->hasMany(IpAddress::class, 'ip_pool_id')->available()->usable();
    }

    /**
     * Get assigned IP addresses in this pool.
     */
    public function assignedIpAddresses(): HasMany
    {
        return $this->hasMany(IpAddress::class, 'ip_pool_id')->assigned();
    }

    /**
     * Scope a query to only include active IP pools.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get the CIDR notation for this IP pool.
     */
    public function getCidrAttribute(): string
    {
        // Convert subnet mask to CIDR prefix
        $subnet_bits = 0;
        $subnet_octets = explode('.', $this->subnet_mask);

        foreach ($subnet_octets as $octet) {
            $subnet_bits += substr_count(decbin((int) $octet), '1');
        }

        return $this->network_address.'/'.$subnet_bits;
    }

    /**
     * Get the total number of IP addresses in this pool.
     */
    public function getTotalIpsAttribute(): int
    {
        return $this->total_addresses ?? 0;
    }

    /**
     * Get the number of used IP addresses in this pool.
     */
    public function getUsedIpsAttribute(): int
    {
        return $this->assigned_addresses ?? $this->staticIpServices()->where('status', 'active')->count();
    }

    /**
     * Get the number of available IP addresses in this pool.
     */
    public function getAvailableIpsAttribute(): int
    {
        $total = $this->total_addresses;
        $used = $this->used_ips;
        $excluded = count($this->excluded_ips ?? []);

        return $total - $used - $excluded;
    }

    /**
     * Get the usage percentage of this IP pool.
     */
    public function getUsagePercentageAttribute(): float
    {
        $total = $this->total_addresses ?? 0;

        if ($total === 0) {
            return 0.0;
        }

        $used = $this->assigned_addresses ?? $this->staticIpServices()->where('status', 'active')->count();

        return round(($used / $total) * 100, 2);
    }

    /**
     * Find the next available IP address in this pool.
     * Uses the original CIDR range for IP calculation, not the forced /24 subnet mask.
     */
    public function getNextAvailableIp(): ?string
    {
        // If no more IPs are available, return null
        if ($this->available_ips <= 0) {
            return null;
        }

        // Use original CIDR range for IP calculation
        $cidr = $this->cidr_range ?: $this->network_cidr;
        if (! $cidr) {
            return null;
        }

        // Extract prefix length from CIDR (e.g., /27 from **********/27)
        if (preg_match('/\/(\d+)$/', $cidr, $matches)) {
            $prefix_length = (int) $matches[1];
        } else {
            // Fallback to subnet mask calculation
            $subnet_binary = '';
            foreach (explode('.', $this->subnet_mask) as $octet) {
                $subnet_binary .= str_pad(decbin((int) $octet), 8, '0', STR_PAD_LEFT);
            }
            $prefix_length = substr_count($subnet_binary, '1');
        }

        // Get all used IPs
        $used_ips = $this->staticIpServices()->pluck('ip_address')->toArray();
        $excluded_ips = $this->excluded_ips ?? [];
        $all_used_ips = array_merge($used_ips, $excluded_ips);

        // If gateway is set, add it to used IPs
        if ($this->gateway) {
            $all_used_ips[] = $this->gateway;
        }

        // Calculate the first and last usable IP addresses based on CIDR range
        $first_ip_long = ip2long($this->network_address) + 1; // Skip network address
        $last_ip_long = ip2long($this->network_address) + pow(2, (32 - $prefix_length)) - 2; // Skip broadcast address

        // Special case for /31 and /32
        if ($prefix_length >= 31) {
            $first_ip_long = ip2long($this->network_address);
            $last_ip_long = ip2long($this->network_address) + pow(2, (32 - $prefix_length)) - 1;
        }

        // Find the next available IP within the CIDR range
        for ($ip_long = $first_ip_long; $ip_long <= $last_ip_long; $ip_long++) {
            $ip = long2ip($ip_long);
            if (! in_array($ip, $all_used_ips)) {
                return $ip;
            }
        }

        return null;
    }

    /**
     * Get all available IP addresses in this pool for dropdown selection.
     */
    public function getAllAvailableIps(): array
    {
        // Use original CIDR range for IP calculation
        $cidr = $this->cidr_range ?: $this->network_cidr;
        if (! $cidr) {
            return [];
        }

        // Extract prefix length from CIDR (e.g., /27 from **********/27)
        if (preg_match('/\/(\d+)$/', $cidr, $matches)) {
            $prefix_length = (int) $matches[1];
        } else {
            // Fallback to subnet mask calculation
            $subnet_binary = '';
            foreach (explode('.', $this->subnet_mask) as $octet) {
                $subnet_binary .= str_pad(decbin((int) $octet), 8, '0', STR_PAD_LEFT);
            }
            $prefix_length = substr_count($subnet_binary, '1');
        }

        // Get all used IPs
        $used_ips = $this->staticIpServices()->pluck('ip_address')->toArray();
        $excluded_ips = $this->excluded_ips ?? [];
        $all_used_ips = array_merge($used_ips, $excluded_ips);

        // If gateway is set, add it to used IPs
        if ($this->gateway) {
            $all_used_ips[] = $this->gateway;
        }

        // Calculate the first and last usable IP addresses based on CIDR range
        $first_ip_long = ip2long($this->network_address) + 1; // Skip network address
        $last_ip_long = ip2long($this->network_address) + pow(2, (32 - $prefix_length)) - 2; // Skip broadcast address

        // Special case for /31 and /32
        if ($prefix_length >= 31) {
            $first_ip_long = ip2long($this->network_address);
            $last_ip_long = ip2long($this->network_address) + pow(2, (32 - $prefix_length)) - 1;
        }

        // Collect all available IPs
        $available_ips = [];
        for ($ip_long = $first_ip_long; $ip_long <= $last_ip_long; $ip_long++) {
            $ip = long2ip($ip_long);
            if (! in_array($ip, $all_used_ips)) {
                $available_ips[] = $ip;
            }
        }

        return $available_ips;
    }
}
