<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Services\Import\Data\CustomerImportData;
use App\Services\Import\ExcelTemplateService;
use App\Services\Import\MassImportService;
use App\Services\MpesaIdService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ImportTemplateController extends Controller
{
    protected $templateService;

    public function __construct(ExcelTemplateService $templateService)
    {
        $this->templateService = $templateService;
    }

    /**
     * Show import documentation page.
     */
    public function documentation()
    {
        return Inertia::render('admin/import/documentation', [
            'templateInfo' => $this->getTemplateInfo(),
            'validationRules' => $this->getValidationRules(),
            'examples' => $this->getExamples(),
        ]);
    }

    /**
     * Download the customer import template.
     */
    public function downloadTemplate()
    {
        try {
            $filepath = $this->templateService->generateTemplate();
            $filename = basename($filepath);

            return response()->download($filepath, $filename, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ])->deleteFileAfterSend(true);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate template: '.$e->getMessage());
        }
    }

    /**
     * Download the customer import template as CSV.
     */
    public function downloadCsvTemplate()
    {
        try {
            $filepath = $this->templateService->generateCsvTemplate();
            $filename = basename($filepath);

            return response()->download($filepath, $filename, [
                'Content-Type' => 'text/csv',
            ])->deleteFileAfterSend(true);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate CSV template: '.$e->getMessage());
        }
    }

    /**
     * Show import upload form.
     */
    public function showImportForm()
    {
        return Inertia::render('admin/import/upload', [
            'templateInfo' => $this->getTemplateInfo(),
        ]);
    }

    /**
     * Process uploaded Excel/CSV file using existing import infrastructure.
     */
    public function processImport(Request $request)
    {
        // Log the incoming request for debugging
        Log::info('Import request received', [
            'has_file' => $request->hasFile('import_file'),
            'file_info' => $request->hasFile('import_file') ? [
                'name' => $request->file('import_file')->getClientOriginalName(),
                'size' => $request->file('import_file')->getSize(),
                'mime' => $request->file('import_file')->getMimeType(),
                'extension' => $request->file('import_file')->getClientOriginalExtension(),
            ] : null,
        ]);

        // Basic validation first
        if (! $request->hasFile('import_file')) {
            return back()->withErrors([
                'import_file' => 'Please select a file to upload.',
            ])->withInput();
        }

        $file = $request->file('import_file');

        // Check file size (10MB max)
        if ($file->getSize() > 10485760) { // 10MB in bytes
            return back()->withErrors([
                'import_file' => 'The file size must not exceed 10MB.',
            ])->withInput();
        }

        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());
        $allowedExtensions = ['xlsx', 'xls', 'csv'];

        if (! in_array($extension, $allowedExtensions)) {
            return back()->withErrors([
                'import_file' => "Invalid file type. Please upload an Excel (.xlsx, .xls) or CSV (.csv) file. Detected: .{$extension}",
            ])->withInput();
        }

        Log::info('File validation passed', [
            'file_name' => $file->getClientOriginalName(),
            'extension' => $extension,
            'size' => $file->getSize(),
        ]);

        try {
            $extension = $file->getClientOriginalExtension();

            // Parse file based on type
            if (in_array($extension, ['xlsx', 'xls'])) {
                $customerData = $this->parseExcelFile($file);
            } else {
                $customerData = $this->parseCsvFile($file);
            }

            // Create CustomerImportData using your existing structure
            $importData = new CustomerImportData;

            // Process each row and add to import data
            foreach ($customerData as $index => $row) {
                try {
                    $normalizedData = $this->normalizeImportRow($row, $index);
                    $importData->addCustomer($normalizedData);
                } catch (\Exception $e) {
                    Log::warning('Failed to normalize import row', [
                        'row_index' => $index,
                        'error' => $e->getMessage(),
                        'row_data' => $row,
                    ]);
                }
            }

            // Validate the import data
            if (! $importData->validate()) {
                $errors = $importData->getValidationErrors();
                Log::error('Import validation failed', ['errors' => $errors]);

                return back()->with('error', 'Import validation failed. Please check your data format.')
                    ->with('validation_errors', $errors);
            }

            // Use your existing MassImportService to process the import
            // FIXED: Now includes organization_id for multi-tenant compliance
            $massImportService = app(MassImportService::class);
            $organizationId = app(\App\Services\OrganizationContext::class)->getId();

            if (!$organizationId) {
                return back()->with('error', 'No organization context available. Please ensure you are logged in and assigned to an organization.');
            }

            $batchId = $massImportService->importCustomers($importData, [
                'chunk_size' => 50, // Process in smaller chunks for Excel imports
                'organization_id' => $organizationId, // FIXED: Multi-tenant organization assignment
            ]);

            Log::info('Excel import initiated', [
                'batch_id' => $batchId,
                'record_count' => $importData->getRecordCount(),
                'file_name' => $file->getClientOriginalName(),
                'organization_id' => $organizationId, // FIXED: Track organization for audit
            ]);

            $organizationName = app(\App\Services\OrganizationContext::class)->getOrganization()->name ?? "Organization {$organizationId}";

            return redirect()->route('admin.import.documentation')
                ->with('success', "Import started successfully! Batch ID: {$batchId}. Processing {$importData->getRecordCount()} customers for {$organizationName}.");

        } catch (\Exception $e) {
            Log::error('Import processing failed', [
                'error' => $e->getMessage(),
                'file_name' => $request->file('import_file')?->getClientOriginalName(),
            ]);

            return back()->with('error', 'Import failed: '.$e->getMessage());
        }
    }

    /**
     * Get template information.
     */
    protected function getTemplateInfo(): array
    {
        return [
            'version' => 'v'.date('Y.m.d'),
            'last_updated' => now()->format('Y-m-d H:i:s'),
            'supported_formats' => ['.xlsx'],
            'max_file_size' => '10MB',
            'max_records' => 1000,
        ];
    }

    /**
     * Get validation rules for documentation.
     */
    protected function getValidationRules(): array
    {
        return [
            [
                'field' => 'customer_name',
                'required' => true,
                'type' => 'Text',
                'rules' => '2-100 characters, letters and spaces only',
                'example' => 'John Doe Smith',
                'notes' => 'Full customer name as it should appear in the system',
            ],
            [
                'field' => 'customer_email',
                'required' => true,
                'type' => 'Email',
                'rules' => 'Valid email format, must be unique',
                'example' => '<EMAIL>',
                'notes' => 'Used for login and communication',
            ],
            [
                'field' => 'customer_phone',
                'required' => true,
                'type' => 'Phone',
                'rules' => 'International format with country code',
                'example' => '+254712345678',
                'notes' => 'Include country code (+254 for Kenya)',
            ],
            [
                'field' => 'network_site',
                'required' => true,
                'type' => 'Text',
                'rules' => 'Must match existing site or will be created',
                'example' => 'Downtown_Site_A',
                'notes' => 'Physical location where service is provided',
            ],
            [
                'field' => 'service_type',
                'required' => true,
                'type' => 'Text',
                'rules' => 'Must be "static_ip" or "pppoe"',
                'example' => 'static_ip',
                'notes' => 'Determines connection method',
            ],
            [
                'field' => 'ip_address',
                'required' => false,
                'type' => 'IP Address',
                'rules' => 'Required if service_type is "static_ip"',
                'example' => '*************',
                'notes' => 'Must be available in IP pools',
            ],
            [
                'field' => 'bandwidth_plan',
                'required' => true,
                'type' => 'Text',
                'rules' => 'Must match existing bandwidth plan',
                'example' => '10Mbps_Unlimited',
                'notes' => 'Determines speed and data limits',
            ],
            [
                'field' => 'monthly_fee',
                'required' => true,
                'type' => 'Number',
                'rules' => 'Decimal format, 100.00 - 50000.00',
                'example' => '1500.00',
                'notes' => 'Monthly service charge in KES',
            ],
            [
                'field' => 'service_start_date',
                'required' => true,
                'type' => 'Date',
                'rules' => 'YYYY-MM-DD format',
                'example' => '2025-01-15',
                'notes' => 'When service begins/began',
            ],
        ];
    }

    /**
     * Get examples for documentation.
     */
    protected function getExamples(): array
    {
        return [
            'static_ip_customer' => [
                'customer_name' => 'John Doe Smith',
                'customer_email' => '<EMAIL>',
                'customer_phone' => '+254712345678',
                'customer_address' => '123 Main Street',
                'customer_city' => 'Nairobi',
                'network_site' => 'Downtown_Site_A',
                'service_type' => 'static_ip',
                'ip_address' => '*************',
                'bandwidth_plan' => '10Mbps_Unlimited',
                'monthly_fee' => '1500.00',
                'service_start_date' => '2025-01-15',
                'mpesa_id' => 'MP001234',
                'customer_status' => 'active',
            ],
            'pppoe_customer' => [
                'customer_name' => 'Jane Wilson',
                'customer_email' => '<EMAIL>',
                'customer_phone' => '+254723456789',
                'customer_address' => '456 Oak Avenue',
                'customer_city' => 'Mombasa',
                'network_site' => 'Coastal_Site_B',
                'service_type' => 'pppoe',
                'ip_address' => '',
                'bandwidth_plan' => '5Mbps_Unlimited',
                'monthly_fee' => '1000.00',
                'service_start_date' => '2025-02-01',
                'mpesa_id' => '',
                'customer_status' => 'active',
            ],
        ];
    }

    /**
     * Parse Excel file and return data array.
     */
    protected function parseExcelFile($file): array
    {
        $spreadsheet = IOFactory::load($file->getPathname());
        $worksheet = $spreadsheet->getActiveSheet();
        $data = $worksheet->toArray();

        // Remove header row and empty rows
        $headers = array_shift($data);
        $data = array_filter($data, function ($row) {
            return ! empty(array_filter($row));
        });

        // Convert to associative array
        $result = [];
        foreach ($data as $row) {
            $rowData = [];
            foreach ($headers as $index => $header) {
                $rowData[trim($header)] = $row[$index] ?? '';
            }
            $result[] = $rowData;
        }

        return $result;
    }

    /**
     * Parse CSV file and return data array.
     */
    protected function parseCsvFile($file): array
    {
        $handle = fopen($file->getPathname(), 'r');
        $headers = fgetcsv($handle);
        $data = [];

        while (($row = fgetcsv($handle)) !== false) {
            if (empty(array_filter($row))) {
                continue;
            } // Skip empty rows

            $rowData = [];
            foreach ($headers as $index => $header) {
                $rowData[trim($header)] = $row[$index] ?? '';
            }
            $data[] = $rowData;
        }

        fclose($handle);

        return $data;
    }

    /**
     * Normalize import row to match CustomerImportData structure.
     */
    protected function normalizeImportRow(array $row, int $index): array
    {
        // Handle M-Pesa ID based on assignment mode
        $mpesaIdService = app(MpesaIdService::class);
        $assignmentMode = \App\Models\SystemSetting::get('mpesa_id_assignment_mode', 'hybrid');

        $mpesaId = null;
        if (isset($row['mpesa_id']) && ! empty($row['mpesa_id'])) {
            if ($assignmentMode === 'manual' || $assignmentMode === 'hybrid') {
                try {
                    $mpesaIdService->validateMpesaId($row['mpesa_id'], null);
                    $mpesaId = $row['mpesa_id'];
                } catch (\Exception $e) {
                    if ($assignmentMode === 'manual') {
                        throw new \Exception("Invalid M-Pesa ID '{$row['mpesa_id']}' at row ".($index + 1));
                    }
                    // In hybrid mode, we'll auto-generate if invalid
                }
            }
        }

        // Map template fields to CustomerImportData structure
        return [
            'name' => $row['Customer Name *'] ?? $row['name'] ?? '',
            'email' => $row['Customer Email *'] ?? $row['email'] ?? '',
            'phone' => $row['Customer Phone'] ?? $row['phone'] ?? '',
            'address' => $row['Customer Address'] ?? $row['address'] ?? '',
            'city' => $row['Customer City'] ?? $row['city'] ?? '',
            'state' => $row['Customer State'] ?? $row['state'] ?? '',
            'postal_code' => $row['Postal Code'] ?? $row['postal_code'] ?? '',
            'country' => $row['Country'] ?? $row['country'] ?? 'Kenya',
            'service_type' => $row['Service Type *'] ?? $row['service_type'] ?? '',
            'ip_address' => $row['IP Address (Static Only)'] ?? $row['ip_address'] ?? '',
            'bandwidth_plan_id' => $row['Bandwidth Plan ID *'] ?? $row['bandwidth_plan_id'] ?? '',
            'network_site_id' => $row['Network Site ID'] ?? $row['network_site_id'] ?? '',
            'device_id' => $row['Device ID'] ?? $row['device_id'] ?? '',
            'ip_pool_id' => $row['IP Pool ID (Static Only)'] ?? $row['ip_pool_id'] ?? '1', // Default to pool 1
            'subscription_price' => $row['Monthly Fee (KES) *'] ?? $row['subscription_price'] ?? '',
            'billing_cycle' => $row['Billing Cycle'] ?? $row['billing_cycle'] ?? 'monthly',
            'start_date' => $row['Service Start Date *'] ?? $row['start_date'] ?? now()->toDateString(),
            'next_billing_date' => $row['Next Billing Date'] ?? $row['next_billing_date'] ?? now()->addMonth()->toDateString(),
            'username' => $row['PPPoE Username'] ?? $row['username'] ?? '',
            'password' => $row['PPPoE Password'] ?? $row['password'] ?? '',
            'external_id' => $row['External ID'] ?? $row['external_id'] ?? '',
            'notes' => $row['Notes'] ?? $row['notes'] ?? '',
            'mpesa_id' => $mpesaId,
            'external_platform' => 'excel_import',
            'import_batch' => 'excel_'.now()->format('Y_m_d_H_i_s'),
        ];
    }
}
