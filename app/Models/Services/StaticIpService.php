<?php

namespace App\Models\Services;

use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Subscription;
use App\Traits\BelongsToOrganization;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class StaticIpService extends Model
{
    use HasFactory, BelongsToOrganization;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'subscription_id',
        'customer_id',
        'device_id',
        'ip_address',
        'subnet_mask',
        'gateway',
        'dns_servers',
        'bandwidth_plan_id',
        'ip_pool_id',
        'status',
        'firewall_rules',
        'nat_rules',
        'comment',
        'mikrotik_route_id',
        'mikrotik_firewall_id',
        'mikrotik_nat_id',
        'mikrotik_id',
        'organization_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'dns_servers' => 'array',
        'firewall_rules' => 'array',
        'nat_rules' => 'array',
    ];

    /**
     * Get the customer that owns the Static IP service.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the subscription associated with the Static IP service.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the device (router) that hosts this Static IP service.
     */
    public function device(): BelongsTo
    {
        return $this->belongsTo(NetworkDevice::class, 'device_id');
    }

    /**
     * Get the bandwidth plan associated with the Static IP service.
     */
    public function bandwidthPlan(): BelongsTo
    {
        return $this->belongsTo(BandwidthPlan::class, 'bandwidth_plan_id');
    }

    /**
     * Get the IP pool this service's IP was allocated from.
     */
    public function ipPool(): BelongsTo
    {
        return $this->belongsTo(IpPool::class, 'ip_pool_id');
    }

    /**
     * Scope a query to only include active Static IP services.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include suspended Static IP services.
     */
    public function scopeSuspended($query)
    {
        return $query->where('status', 'suspended');
    }

    /**
     * Scope a query to only include services for a specific customer.
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope a query to only include services on a specific device.
     */
    public function scopeOnDevice($query, $deviceId)
    {
        return $query->where('device_id', $deviceId);
    }

    /**
     * Check if the service is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the service is suspended.
     */
    public function isSuspended(): bool
    {
        return $this->status === 'suspended';
    }

    /**
     * Activate the Static IP service.
     */
    public function activate(): bool
    {
        if ($this->status !== 'active') {
            $this->status = 'active';

            return $this->save();
        }

        return true;
    }

    /**
     * Suspend the Static IP service.
     */
    public function suspend(): bool
    {
        if ($this->status !== 'suspended') {
            $this->status = 'suspended';

            return $this->save();
        }

        return true;
    }

    /**
     * Get the CIDR notation for this IP address and subnet.
     */
    public function getCidrAttribute(): string
    {
        // Convert subnet mask to CIDR prefix
        $subnet_bits = 0;
        $subnet_octets = explode('.', $this->subnet_mask);

        foreach ($subnet_octets as $octet) {
            $subnet_bits += substr_count(decbin((int) $octet), '1');
        }

        return $this->ip_address.'/'.$subnet_bits;
    }

    /**
     * Get the network address for this IP and subnet.
     */
    public function getNetworkAddressAttribute(): string
    {
        $ip_octets = explode('.', $this->ip_address);
        $subnet_octets = explode('.', $this->subnet_mask);
        $network_octets = [];

        for ($i = 0; $i < 4; $i++) {
            $network_octets[] = (int) $ip_octets[$i] & (int) $subnet_octets[$i];
        }

        return implode('.', $network_octets);
    }
}
