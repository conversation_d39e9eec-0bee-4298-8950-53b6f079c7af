# 🔄 ISP Management System - Update System Guide

This guide explains how to use the automated update system to keep your ISP Management System current with the latest features and security updates.

## 🎯 **Update System Overview**

### **Key Features**
- ✅ **Automated Updates**: One-command system updates
- ✅ **Data Protection**: Automatic backups before updates
- ✅ **Rollback Capability**: Easy rollback to previous versions
- ✅ **Customization Preservation**: Your customizations survive updates
- ✅ **Zero Downtime**: Minimal service interruption
- ✅ **Update Notifications**: Automatic alerts for new versions

### **Update Components**
- **Core Application**: Laravel framework and ISP-specific code
- **Database Schema**: New tables, columns, and indexes
- **Dependencies**: PHP packages and system libraries
- **Configuration**: New settings and options
- **Assets**: Frontend resources and themes

## 🔍 **Checking for Updates**

### **Manual Update Check**
```bash
# Check for available updates
./check-updates.sh

# Output example:
# 🔍 ISP Management System - Update Status
# Current Version: v1.0.0
# 🔄 Update Available!
# Latest Version: v1.1.0 (Accounting Module)
# Commits Behind: 15
```

### **Automatic Update Checking**
The system automatically checks for updates daily at 2 AM:

```bash
# View automatic check logs
tail -f storage/logs/update-checks.log

# Setup automatic checking (done during installation)
./check-updates.sh --setup
```

### **Update Notifications**
When updates are available, you'll receive:
- **Dashboard notification** in admin panel
- **Email alert** (if configured)
- **Log entries** in update check log

## 🚀 **Applying Updates**

### **Standard Update Process**

```bash
# Apply the latest update
./update.sh
```

**What happens during update**:
1. **Backup Creation**: Automatic backup of database and files
2. **Maintenance Mode**: System temporarily unavailable to users
3. **Code Download**: Latest version downloaded from repository
4. **Database Migration**: New features and schema updates applied
5. **Dependency Update**: PHP packages and libraries updated
6. **Cache Clearing**: Application caches refreshed
7. **Service Restart**: Application services restarted
8. **Verification**: System health check performed
9. **Maintenance Mode Off**: System back online

### **Update Output Example**
```bash
🚀 ISP Management System Updater
=================================
✅ Current version: v1.0.0
✅ Latest version available: v1.1.0
⚠️  This will update your system from v1.0.0 to v1.1.0
⚠️  A backup will be created automatically

Do you want to continue? (y/N): y

✅ Creating rollback point: rollback-20250128-143022
✅ Creating backup...
✅ Database backup created
✅ Enabling maintenance mode...
✅ Downloading updates...
✅ Updating dependencies...
✅ Running database migrations...
✅ Clearing caches...
✅ Running post-update scripts...
✅ Restarting services...
✅ Disabling maintenance mode...

🎉 Update completed successfully!
✅ Version: v1.0.0 → v1.1.0
📁 Backup: ./backups/updates/backup-20250128-143022
🔄 Rollback point: rollback-20250128-143022
```

## 🔙 **Rollback System**

### **When to Use Rollback**
- Update caused system issues
- New version has bugs affecting your operations
- Need to revert to previous functionality
- Database migration failed

### **Rollback Process**

```bash
# List available rollback points
./rollback.sh

# Rollback to specific version
./rollback.sh rollback-20250128-143022

# Rollback to latest rollback point
./rollback.sh latest
```

### **Rollback Options**
During rollback, you can choose:
- **Code only**: Rollback application code only
- **Code + Database**: Rollback both code and database
- **Selective restore**: Choose specific components to restore

### **Rollback Output Example**
```bash
🔄 ISP Management System Rollback
=================================
✅ Current version: v1.1.0
✅ Rolling back to: rollback-20250128-143022
📁 Found backup: ./backups/updates/backup-20250128-143022

⚠️  This will rollback your system to rollback-20250128-143022
⚠️  Current data and customizations will be preserved

Do you want to continue? (y/N): y

✅ Creating pre-rollback backup...
✅ Enabling maintenance mode...
✅ Rolling back code...
✅ Restoring files from backup...
✅ Database backup found from rollback point
Do you want to restore the database? (y/N): n
✅ Database not restored - keeping current data
✅ Updating dependencies...
✅ Clearing caches...
✅ Restarting services...
✅ Disabling maintenance mode...

🎉 Rollback completed successfully!
```

## 🛡️ **Data Protection**

### **Automatic Backups**
Before each update, the system creates:

```
./backups/updates/backup-YYYYMMDD-HHMMSS/
├── .env                    # Environment configuration
├── storage/                # Application data and logs
├── custom/                 # Your customizations
├── public/storage/         # Uploaded files
└── database.sql           # Complete database dump
```

### **Backup Retention**
- **Update backups**: Kept for 30 days
- **Rollback points**: Last 5 rollback points kept
- **Manual backups**: Kept indefinitely

### **Manual Backup**
```bash
# Create manual backup before major changes
./scripts/backup.sh

# Restore from specific backup
./scripts/restore.sh backup-20250128-143022
```

## 🎨 **Customization Preservation**

### **Protected Customizations**
The update system preserves:

```
custom/
├── themes/                 # Custom themes and branding
├── config/                 # Configuration overrides
├── views/                  # Custom view templates
├── assets/                 # Custom CSS, JS, images
├── plugins/                # Custom plugins
└── scripts/                # Custom automation scripts
```

### **How Customizations Work**
1. **Before Update**: System backs up custom/ directory
2. **During Update**: Core system files updated
3. **After Update**: Custom files restored and applied
4. **Post-Update**: Custom configurations merged

### **Custom Configuration Example**
```php
// custom/config/app.php
<?php
return [
    'name' => 'Acme ISP Management',
    'timezone' => 'Africa/Nairobi',
    'locale' => 'en',
    // Your custom settings override defaults
];
```

## 📋 **Version Management**

### **Version Numbering**
The system uses semantic versioning:
- **Major (v2.0.0)**: Breaking changes, major new features
- **Minor (v1.1.0)**: New features, backward compatible
- **Patch (v1.0.1)**: Bug fixes, security updates

### **Release Types**
- **🚀 Major Release**: New modules (accounting, CRM)
- **✨ Minor Release**: New features (reports, integrations)
- **🔧 Patch Release**: Bug fixes, security updates
- **🚨 Security Release**: Critical security patches

### **Release Notes**
Each version includes detailed release notes:
```
docs/releases/v1.1.0.md
├── Summary of changes
├── New features
├── Bug fixes
├── Database changes
├── Upgrade instructions
└── Breaking changes (if any)
```

## 🔧 **Advanced Update Options**

### **Selective Updates**
For advanced users, you can update specific components:

```bash
# Update only dependencies
docker-compose run --rm app composer update

# Update only database
docker-compose run --rm app php artisan migrate

# Update only caches
docker-compose run --rm app php artisan optimize
```

### **Staging Updates**
Test updates in staging environment:

```bash
# Create staging environment
cp -r /path/to/production /path/to/staging
cd /path/to/staging

# Test update in staging
./update.sh

# If successful, apply to production
cd /path/to/production
./update.sh
```

### **Custom Post-Update Scripts**
Add custom actions after updates:

```bash
# Create custom post-update script
nano custom/scripts/post-update.sh

#!/bin/bash
# Your custom post-update actions
echo "Running custom post-update tasks..."
# Custom logic here
```

## 📊 **Update Monitoring**

### **Update Logs**
Monitor update activities:

```bash
# View update logs
tail -f storage/logs/updates.log

# View system logs during update
docker-compose logs -f app
```

### **Health Checks**
After updates, verify system health:

```bash
# Check application status
docker-compose ps

# Test database connection
docker-compose exec app php artisan migrate:status

# Verify web interface
curl -I https://your-domain.com
```

### **Performance Monitoring**
Monitor system performance after updates:
- Response times
- Database query performance
- Memory usage
- Error rates

## 🆘 **Troubleshooting Updates**

### **Common Issues**

#### **Update Fails to Download**
```bash
# Check internet connection
ping github.com

# Verify repository access
git fetch origin

# Check disk space
df -h
```

#### **Database Migration Fails**
```bash
# Check database connection
docker-compose exec app php artisan tinker
# Test: DB::connection()->getPdo();

# View migration status
docker-compose exec app php artisan migrate:status

# Rollback failed migration
docker-compose exec app php artisan migrate:rollback
```

#### **Services Won't Start**
```bash
# Check container logs
docker-compose logs app

# Restart services
docker-compose restart

# Rebuild containers if needed
docker-compose up --build -d
```

### **Recovery Procedures**

#### **Complete System Recovery**
If update completely fails:

```bash
# 1. Stop all services
docker-compose down

# 2. Restore from backup
./scripts/restore.sh backup-YYYYMMDD-HHMMSS

# 3. Restart services
docker-compose up -d

# 4. Verify system
./check-updates.sh
```

#### **Partial Recovery**
If only specific components fail:

```bash
# Restore specific files
cp -r backups/updates/latest/custom/ ./

# Restore database only
docker-compose exec -T postgres psql -U isp_user -d isp_management < backup.sql

# Clear caches
docker-compose exec app php artisan optimize:clear
```

## 📋 **Update Best Practices**

### **Before Updating**
- [ ] Read release notes
- [ ] Test in staging environment
- [ ] Notify users of maintenance window
- [ ] Verify backup system is working
- [ ] Check system resources (disk space, memory)

### **During Update**
- [ ] Monitor update progress
- [ ] Watch for error messages
- [ ] Keep terminal session active
- [ ] Don't interrupt the process

### **After Update**
- [ ] Verify system functionality
- [ ] Test critical features
- [ ] Check customizations
- [ ] Monitor system performance
- [ ] Update documentation

### **Maintenance Schedule**
- **Security Updates**: Apply immediately
- **Minor Updates**: Monthly maintenance window
- **Major Updates**: Quarterly, with thorough testing
- **Emergency Updates**: As needed for critical issues

---

**Update System Guide Complete!** 🔄

Your ISP Management System will now stay current with the latest features and security updates while preserving your data and customizations. For additional help, contact support or refer to the troubleshooting section.
