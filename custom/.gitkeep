# Custom Files Directory

This directory is for ISP provider customizations that should be preserved during updates.

## Structure:
- `themes/` - Custom themes and branding
- `config/` - Custom configuration overrides
- `views/` - Custom view templates
- `assets/` - Custom CSS, JS, images
- `plugins/` - Custom plugins or extensions
- `scripts/` - Custom automation scripts

## Important:
- Files in this directory are automatically backed up during updates
- These files take precedence over default system files
- Use this for client-specific customizations that should survive updates
