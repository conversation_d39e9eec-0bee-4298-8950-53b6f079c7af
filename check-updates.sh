#!/bin/bash

# ISP Management System - Update Checker
# This script checks for available updates and notifies administrators

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[INFO]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_header() { echo -e "${BLUE}$1${NC}"; }

# Configuration
VERSION_FILE="./VERSION"
UPDATE_CHECK_LOG="./storage/logs/update-checks.log"
NOTIFICATION_FILE="./storage/app/update-notification.json"

# Create necessary directories
mkdir -p "$(dirname "$UPDATE_CHECK_LOG")" "$(dirname "$NOTIFICATION_FILE")"

# Logging function
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$UPDATE_CHECK_LOG"
    if [[ "${2:-true}" == "true" ]]; then
        echo "$1"
    fi
}

# Function to get current version
get_current_version() {
    if [[ -f "$VERSION_FILE" ]]; then
        grep "Version:" "$VERSION_FILE" | cut -d' ' -f2 || echo "unknown"
    else
        echo "unknown"
    fi
}

# Function to check for updates
check_for_updates() {
    local current_version=$(get_current_version)
    
    log_message "Checking for updates... Current version: $current_version"
    
    # Fetch latest information from remote
    if ! git fetch origin --tags >/dev/null 2>&1; then
        log_message "Failed to fetch updates from remote repository"
        return 1
    fi
    
    # Get latest version
    local latest_version=$(git describe --tags --abbrev=0 origin/main 2>/dev/null || echo "")
    
    if [[ -z "$latest_version" ]]; then
        log_message "No version tags found in remote repository"
        return 1
    fi
    
    log_message "Latest available version: $latest_version"
    
    # Compare versions
    if [[ "$current_version" == "$latest_version" ]]; then
        log_message "System is up to date"
        
        # Clear any existing notifications
        rm -f "$NOTIFICATION_FILE"
        
        return 0
    else
        log_message "Update available: $current_version -> $latest_version"
        
        # Create notification
        create_update_notification "$current_version" "$latest_version"
        
        return 2  # Update available
    fi
}

# Function to create update notification
create_update_notification() {
    local current_version=$1
    local latest_version=$2
    
    # Get release notes if available
    local release_notes=""
    local release_notes_file="docs/releases/${latest_version}.md"
    
    if git show "origin/main:$release_notes_file" >/dev/null 2>&1; then
        release_notes=$(git show "origin/main:$release_notes_file" 2>/dev/null || echo "Release notes not available")
    fi
    
    # Count commits since current version
    local commits_behind=0
    if git rev-parse "$current_version" >/dev/null 2>&1; then
        commits_behind=$(git rev-list --count "$current_version"..origin/main 2>/dev/null || echo "0")
    fi
    
    # Create notification JSON
    cat > "$NOTIFICATION_FILE" << EOF
{
    "update_available": true,
    "current_version": "$current_version",
    "latest_version": "$latest_version",
    "commits_behind": $commits_behind,
    "check_date": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "release_notes_available": $([ -n "$release_notes" ] && echo "true" || echo "false"),
    "update_command": "./update.sh",
    "release_notes_file": "$release_notes_file"
}
EOF
    
    log_message "Update notification created"
}

# Function to display update status
display_update_status() {
    local current_version=$(get_current_version)
    
    print_header "🔍 ISP Management System - Update Status"
    print_header "========================================"
    
    print_status "Current Version: $current_version"
    
    # Check for updates
    local update_status
    check_for_updates
    update_status=$?
    
    case $update_status in
        0)
            print_status "✅ Your system is up to date!"
            ;;
        2)
            if [[ -f "$NOTIFICATION_FILE" ]]; then
                local latest_version=$(grep '"latest_version"' "$NOTIFICATION_FILE" | cut -d'"' -f4)
                local commits_behind=$(grep '"commits_behind"' "$NOTIFICATION_FILE" | cut -d':' -f2 | cut -d',' -f1 | tr -d ' ')
                
                print_warning "🔄 Update Available!"
                print_warning "Latest Version: $latest_version"
                print_warning "Commits Behind: $commits_behind"
                echo ""
                print_status "To update, run: ./update.sh"
                print_status "To see what's new: git log --oneline HEAD..origin/main"
                
                # Show recent commits
                echo ""
                print_status "Recent changes:"
                git log --oneline --max-count=5 HEAD..origin/main 2>/dev/null || echo "Unable to show recent changes"
            fi
            ;;
        *)
            print_error "❌ Unable to check for updates"
            print_error "Please check your internet connection and repository access"
            ;;
    esac
    
    echo ""
    print_status "📋 Update History:"
    if [[ -f "$UPDATE_CHECK_LOG" ]]; then
        tail -5 "$UPDATE_CHECK_LOG" | while read -r line; do
            echo "  $line"
        done
    else
        echo "  No update history available"
    fi
}

# Function to run automated check (for cron)
run_automated_check() {
    log_message "Running automated update check" false
    
    local update_status
    check_for_updates >/dev/null 2>&1
    update_status=$?
    
    if [[ $update_status -eq 2 ]]; then
        # Update available - send notification
        log_message "Automated check: Update available" false
        
        # If email is configured, send notification
        if command -v mail >/dev/null 2>&1 && [[ -n "${ADMIN_EMAIL:-}" ]]; then
            local current_version=$(get_current_version)
            local latest_version=$(grep '"latest_version"' "$NOTIFICATION_FILE" | cut -d'"' -f4 2>/dev/null || echo "unknown")
            
            cat << EOF | mail -s "ISP Management System Update Available" "$ADMIN_EMAIL"
An update is available for your ISP Management System.

Current Version: $current_version
Latest Version: $latest_version

To update your system, run:
./update.sh

For more information, check the system dashboard or run:
./check-updates.sh

This is an automated message from your ISP Management System.
EOF
            log_message "Email notification sent to $ADMIN_EMAIL" false
        fi
    else
        log_message "Automated check: No updates available" false
    fi
}

# Function to setup automatic checking
setup_auto_check() {
    print_status "Setting up automatic update checking..."
    
    # Create cron job entry
    local cron_entry="0 2 * * * cd $(pwd) && ./check-updates.sh --auto >/dev/null 2>&1"
    
    # Check if cron job already exists
    if crontab -l 2>/dev/null | grep -q "check-updates.sh"; then
        print_warning "Automatic update checking is already configured"
    else
        # Add cron job
        (crontab -l 2>/dev/null; echo "$cron_entry") | crontab -
        print_status "✅ Automatic update checking configured (daily at 2 AM)"
    fi
    
    print_status "To disable automatic checking, run: crontab -e"
}

# Main execution
case "${1:-status}" in
    "--auto"|"auto")
        run_automated_check
        ;;
    "--setup"|"setup")
        setup_auto_check
        ;;
    "status"|*)
        display_update_status
        ;;
esac
