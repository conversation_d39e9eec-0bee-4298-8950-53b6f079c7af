# 🚀 ISP Management System - Installation Guide

This guide will walk you through installing the ISP Management System on your server.

## 📋 **Prerequisites**

### **System Requirements**
- **Operating System**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM**: Minimum 2GB, Recommended 4GB+
- **Storage**: Minimum 20GB, Recommended 50GB+
- **CPU**: 2+ cores recommended
- **Network**: Stable internet connection

### **Required Software**
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Git**: Latest version
- **Domain Name**: For production deployment

### **Network Requirements**
- **Port 80**: HTTP traffic
- **Port 443**: HTTPS traffic (SSL)
- **Port 22**: SSH access
- **Port 5432**: PostgreSQL (internal)
- **Port 6379**: Redis (internal)

## 🛠️ **Pre-Installation Setup**

### **1. Update Your System**
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### **2. Install Docker**
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y docker docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

### **3. Install Docker Compose**
```bash
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### **4. Install Git**
```bash
# Ubuntu/Debian
sudo apt install git -y

# CentOS/RHEL
sudo yum install git -y
```

### **5. Verify Installation**
```bash
docker --version
docker-compose --version
git --version
```

## 📦 **Installation Process**

### **Step 1: Download the System**
```bash
# Clone the repository (you'll need access credentials)
git clone https://github.com/your-company/isp-management-system.git
cd isp-management-system

# Or extract from provided package
tar -xzf isp-management-v1.0.0.tar.gz
cd isp-management-v1.0.0
```

### **Step 2: Run the Installer**
```bash
# Make installer executable
chmod +x install.sh

# Run the installation
./install.sh
```

### **Step 3: Follow Installation Prompts**
The installer will ask for:

1. **ISP Company Name**: Your company name (e.g., "Acme Internet Services")
2. **Domain Name**: Your domain (e.g., "manage.acmeisp.com")
3. **Admin Email**: Administrator email address
4. **Database Name**: Database name (default: isp_management)

### **Step 4: Wait for Installation**
The installer will:
- ✅ Generate secure passwords
- ✅ Configure environment files
- ✅ Set up Docker containers
- ✅ Install dependencies
- ✅ Run database migrations
- ✅ Create admin user
- ✅ Configure update system

## 🔐 **Post-Installation Security**

### **1. Change Default Passwords**
```bash
# Access your system at: https://your-domain.com
# Login with:
# Email: <EMAIL>
# Password: admin123

# IMMEDIATELY change this password in the admin panel
```

### **2. Configure SSL Certificate**
```bash
# For production, set up SSL certificate
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### **3. Configure Firewall**
```bash
# Ubuntu/Debian with UFW
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL with firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 🔧 **Configuration**

### **1. Environment Configuration**
Edit `.env` file for custom settings:
```bash
nano .env
```

Key settings to review:
```env
# Application
APP_NAME="Your ISP Name"
APP_URL=https://your-domain.com

# Database (auto-configured)
DB_CONNECTION=pgsql
DB_HOST=postgres
DB_DATABASE=isp_management

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-server.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# M-Pesa Configuration (Kenya)
MPESA_CONSUMER_KEY=your-consumer-key
MPESA_CONSUMER_SECRET=your-consumer-secret
```

### **2. Restart Services After Configuration**
```bash
docker-compose restart
```

## ✅ **Verification**

### **1. Check System Status**
```bash
# Check all containers are running
docker-compose ps

# Check logs
docker-compose logs app
```

### **2. Access the System**
- **URL**: https://your-domain.com
- **Admin Email**: <EMAIL>
- **Password**: admin123 (change immediately)

### **3. Test Core Functions**
1. ✅ Login to admin panel
2. ✅ Create a test customer
3. ✅ Set up a bandwidth plan
4. ✅ Configure a network device
5. ✅ Test MikroTik connection

## 🔄 **Update System Setup**

The update system is automatically configured during installation:

```bash
# Check for updates
./check-updates.sh

# Update system
./update.sh

# Rollback if needed
./rollback.sh [version]
```

## 🆘 **Troubleshooting**

### **Common Issues**

#### **Docker Permission Denied**
```bash
sudo usermod -aG docker $USER
# Logout and login again
```

#### **Port Already in Use**
```bash
# Check what's using the port
sudo netstat -tulpn | grep :80

# Stop conflicting service
sudo systemctl stop apache2  # or nginx
```

#### **Database Connection Failed**
```bash
# Check PostgreSQL container
docker-compose logs postgres

# Restart database
docker-compose restart postgres
```

#### **SSL Certificate Issues**
```bash
# Check certificate status
sudo certbot certificates

# Renew certificate
sudo certbot renew
```

### **Getting Help**
1. **Check logs**: `docker-compose logs [service]`
2. **Review documentation**: See docs/ folder
3. **Contact support**: Create GitHub issue
4. **Emergency support**: Email provided during purchase

## 📋 **Next Steps**

After successful installation:

1. **📖 Read**: [Configuration Guide](CONFIGURATION.md)
2. **🔧 Setup**: [MikroTik Integration](MIKROTIK-SETUP.md)
3. **👥 Configure**: [User Management](USER-MANAGEMENT.md)
4. **💰 Setup**: [Billing Configuration](BILLING-SETUP.md)
5. **📊 Learn**: [Using the System](USER-GUIDE.md)

## 🔒 **Security Checklist**

- [ ] Changed default admin password
- [ ] Configured SSL certificate
- [ ] Set up firewall rules
- [ ] Configured email settings
- [ ] Set up regular backups
- [ ] Reviewed user permissions
- [ ] Tested update system

---

**Installation Complete!** 🎉

Your ISP Management System is now ready to help you manage your internet service provider operations efficiently.
