# Multi-Tenant Organization Implementation

This document describes the comprehensive multi-tenant organization-based data isolation system implemented in the ISP management application.

## Overview

The system provides complete data isolation between organizations while maintaining all existing functionality within each organization's scope. Each organization can only access their own data across all features: customer management, service provisioning, billing, payments, MikroTik device management, etc.

## Architecture Components

### 1. Database Schema

#### Organizations Table
- `id` - Primary key
- `name` - Organization name
- `slug` - Unique URL-friendly identifier
- `description` - Optional description
- `settings` - JSON field for organization-specific settings
- `status` - Organization status (active, inactive, suspended)
- `created_at`, `updated_at` - Timestamps

#### Organization Foreign Keys
All major tables now include `organization_id` foreign key:
- `users` - Users belong to organizations
- `customers` - Customer data isolation
- `subscriptions` - Subscription isolation
- `invoices`, `invoice_items` - Billing isolation
- `payments` - Payment isolation
- `bandwidth_plans` - Plan isolation
- `network_sites`, `network_devices` - Infrastructure isolation
- `ip_pools`, `static_ip_services`, `pppoe_services` - Service isolation
- All other related tables

### 2. Core Classes

#### Organization Model (`app/Models/Organization.php`)
- Manages organization data and relationships
- Provides helper methods for status checking
- Includes relationships to all organization-owned data

#### BelongsToOrganization Trait (`app/Traits/BelongsToOrganization.php`)
- **Automatic Query Scoping**: All SELECT queries automatically filtered by organization
- **Automatic Assignment**: New records automatically assigned to current organization
- **Ownership Validation**: UPDATE/DELETE operations validated for organization ownership
- **Scope Management**: Methods to bypass scoping for admin operations

#### OrganizationContext Service (`app/Services/OrganizationContext.php`)
- Manages current organization context throughout the application
- Loads organization from authenticated user
- Provides organization switching capabilities
- Handles organization-aware operations

#### SetOrganizationContext Middleware (`app/Http/Middleware/SetOrganizationContext.php`)
- Sets organization context for authenticated users
- Validates organization status
- Handles authentication failures for inactive organizations

### 3. Data Migration

#### Migration Strategy
1. **Create Organizations Table**: Base organization structure
2. **Add Foreign Keys**: Add `organization_id` to all tables
3. **Data Migration**: Assign existing data to default organization
4. **Constraint Updates**: Make `organization_id` NOT NULL after migration

#### Default Organization
- Created during migration for existing data
- Name: "Default Organization"
- Slug: "default"
- All existing data assigned to this organization

## Implementation Details

### Model Updates

All models now include the `BelongsToOrganization` trait:

```php
use App\Traits\BelongsToOrganization;

class Customer extends Model
{
    use HasFactory, BelongsToOrganization;
    
    protected $fillable = [
        // ... existing fields
        'organization_id',
    ];
}
```

### Automatic Scoping

The trait automatically applies organization scoping:

```php
// This query is automatically scoped to current organization
$customers = Customer::all();

// Equivalent to:
$customers = Customer::where('organization_id', auth()->user()->organization_id)->get();
```

### Bypassing Scoping

For admin operations, scoping can be bypassed:

```php
// Get all customers across all organizations (admin only)
$allCustomers = Customer::withoutOrganizationScope()->get();

// Execute callback without organization scoping
$organizationContext->withoutOrganization(function() {
    // Operations here are not scoped to any organization
});
```

### Frontend Integration

Organization context is shared with frontend:

```typescript
interface SharedData {
    organization: {
        id: number | null;
        name: string | null;
        slug: string | null;
    };
}
```

## Management Commands

### Organization Management
```bash
# Create new organization
php artisan organization:manage create --name="New ISP" --description="Description"

# List all organizations
php artisan organization:manage list

# Create user for organization
php artisan organization:manage create-user --org-id=1 --user-name="Admin" --user-email="<EMAIL>"

# Assign existing user to organization
php artisan organization:manage assign-user --user-email="<EMAIL>" --org-id=1
```

## Security Features

### Data Isolation
- **Complete Separation**: Organizations cannot access each other's data
- **Automatic Enforcement**: Scoping applied at model level, not controller level
- **Validation**: All operations validated for organization ownership
- **Audit Trail**: Organization context tracked throughout application

### Authentication Integration
- Users must belong to an organization to access the system
- Organization status checked on every request
- Inactive organizations automatically logged out

### Error Handling
- Graceful handling of organization access violations
- Clear error messages for unauthorized access attempts
- Fallback mechanisms for edge cases

## Testing

### Sample Organizations
The seeder creates test organizations:
- Default Organization (for migrated data)
- Acme ISP
- TechNet Solutions  
- Global Connect

Each organization includes an admin user for testing.

### Verification
```bash
# Test organization context loading
php artisan tinker --execute="app(\App\Services\OrganizationContext::class)"

# Verify data isolation by logging in as different organization users
```

## Migration Guide

### For Existing Installations

1. **Backup Database**: Always backup before migration
2. **Run Migrations**: `php artisan migrate`
3. **Seed Organizations**: `php artisan db:seed --class=OrganizationSeeder`
4. **Assign Users**: Use organization commands to assign users to organizations
5. **Test Isolation**: Verify data isolation by testing with different organization users

### For New Installations

The system is ready to use immediately with proper organization isolation.

## Best Practices

### Development
- Always test with multiple organizations
- Use organization context service for organization-aware operations
- Avoid bypassing scoping unless absolutely necessary
- Test data isolation thoroughly

### Production
- Monitor organization context performance
- Regularly audit organization assignments
- Implement organization-specific backups if needed
- Consider organization-based rate limiting

## Performance Considerations

### Database Indexes
- All `organization_id` columns are indexed
- Composite indexes on frequently queried combinations
- Query performance optimized for organization scoping

### Caching
- Organization context cached per request
- Consider organization-aware caching strategies
- Cache invalidation per organization

## Future Enhancements

### Potential Features
- Organization-specific themes/branding
- Cross-organization data sharing (with permissions)
- Organization hierarchy/sub-organizations
- Organization-specific feature flags
- Advanced organization analytics

### Scalability
- Database sharding by organization
- Organization-specific microservices
- Load balancing by organization
- Geographic organization distribution

## Troubleshooting

### Common Issues
1. **User without organization**: Assign user to organization using management command
2. **Data not showing**: Verify user's organization assignment
3. **Access denied errors**: Check organization ownership of records
4. **Migration issues**: Ensure all tables have organization_id column

### Debug Commands
```bash
# Check user's organization
php artisan tinker --execute="User::find(1)->organization"

# Verify organization context
php artisan tinker --execute="app(\App\Services\OrganizationContext::class)->getOrganization()"
```

This implementation provides a robust, secure, and scalable multi-tenant architecture that maintains complete data isolation while preserving all existing functionality.
