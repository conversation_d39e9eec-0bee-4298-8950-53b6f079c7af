<?php

namespace App\Models\Network;

use App\Traits\BelongsToOrganization;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class NetworkSite extends Model
{
    use HasFactory, BelongsToOrganization;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'address',
        'status',
        'organization_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // No special casting needed for simplified fields
    ];

    // Removed parent/child site relationships for simplicity

    /**
     * Get all devices at this site.
     */
    public function devices(): HasMany
    {
        return $this->hasMany(NetworkDevice::class, 'site_id');
    }

    /**
     * Get all maps for this site.
     */
    public function maps(): Has<PERSON>any
    {
        return $this->hasMany(NetworkMap::class, 'site_id');
    }

    /**
     * Scope a query to only include active sites.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Removed location-based scopes and hierarchical methods since those fields were removed
}
