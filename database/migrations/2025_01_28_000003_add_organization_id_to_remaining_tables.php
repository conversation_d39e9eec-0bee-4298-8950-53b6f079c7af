<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add organization_id to bandwidth usage and related tables
        if (Schema::hasTable('bandwidth_usage')) {
            Schema::table('bandwidth_usage', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }

        if (Schema::hasTable('bandwidth_assignments')) {
            Schema::table('bandwidth_assignments', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }

        if (Schema::hasTable('bandwidth_policies')) {
            Schema::table('bandwidth_policies', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }

        if (Schema::hasTable('bandwidth_quotas')) {
            Schema::table('bandwidth_quotas', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }

        if (Schema::hasTable('bandwidth_rules')) {
            Schema::table('bandwidth_rules', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }

        // Add organization_id to network infrastructure tables
        if (Schema::hasTable('network_interfaces')) {
            Schema::table('network_interfaces', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }

        if (Schema::hasTable('network_connections')) {
            Schema::table('network_connections', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }

        if (Schema::hasTable('network_maps')) {
            Schema::table('network_maps', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }

        if (Schema::hasTable('network_map_items')) {
            Schema::table('network_map_items', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }

        // Add organization_id to IP address management
        if (Schema::hasTable('ip_addresses')) {
            Schema::table('ip_addresses', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }

        // Add organization_id to queue usage tracking
        if (Schema::hasTable('queue_usage')) {
            Schema::table('queue_usage', function (Blueprint $table) {
                $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                $table->index(['organization_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $tables = [
            'queue_usage',
            'ip_addresses',
            'network_map_items',
            'network_maps',
            'network_connections',
            'network_interfaces',
            'bandwidth_rules',
            'bandwidth_quotas',
            'bandwidth_policies',
            'bandwidth_assignments',
            'bandwidth_usage'
        ];

        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                Schema::table($table, function (Blueprint $tableSchema) {
                    if (Schema::hasColumn($table, 'organization_id')) {
                        $tableSchema->dropForeign(['organization_id']);
                        $tableSchema->dropIndex(['organization_id']);
                        $tableSchema->dropColumn('organization_id');
                    }
                });
            }
        }
    }
};
