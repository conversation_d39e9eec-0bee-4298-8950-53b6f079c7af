#!/bin/bash

# ISP Management System - Client Package Creator
# This script creates a clean package for distribution to ISP clients

set -e

echo "📦 ISP Management System - Client Package Creator"
echo "================================================"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get client information
read -p "Enter client name (e.g., 'Acme ISP'): " CLIENT_NAME
read -p "Enter package version (e.g., 'v1.0.0'): " VERSION

# Create clean package directory
PACKAGE_DIR="isp-management-${VERSION}"
print_status "Creating package directory: $PACKAGE_DIR"

# Remove existing package if it exists
rm -rf "$PACKAGE_DIR"
mkdir -p "$PACKAGE_DIR"

# Copy essential files
print_status "Copying application files..."

# Core application files
cp -r app/ "$PACKAGE_DIR/"
cp -r bootstrap/ "$PACKAGE_DIR/"
cp -r config/ "$PACKAGE_DIR/"
cp -r database/ "$PACKAGE_DIR/"
cp -r public/ "$PACKAGE_DIR/"
cp -r resources/ "$PACKAGE_DIR/"
cp -r routes/ "$PACKAGE_DIR/"
cp -r storage/ "$PACKAGE_DIR/"

# Package files
cp composer.json "$PACKAGE_DIR/"
cp composer.lock "$PACKAGE_DIR/"
cp artisan "$PACKAGE_DIR/"
cp .env.example "$PACKAGE_DIR/"

# Docker and deployment files
cp docker-compose.yml "$PACKAGE_DIR/"
cp Dockerfile "$PACKAGE_DIR/"
cp install.sh "$PACKAGE_DIR/"

# Documentation
cp README.md "$PACKAGE_DIR/"
cp -r docs/ "$PACKAGE_DIR/" 2>/dev/null || print_warning "docs/ directory not found, skipping"

# Create deployment directory structure
mkdir -p "$PACKAGE_DIR/deployment/nginx"
mkdir -p "$PACKAGE_DIR/deployment/php"
mkdir -p "$PACKAGE_DIR/deployment/supervisor"
mkdir -p "$PACKAGE_DIR/deployment/ssl"
mkdir -p "$PACKAGE_DIR/scripts"
mkdir -p "$PACKAGE_DIR/backups"

# Create basic nginx config
cat > "$PACKAGE_DIR/deployment/nginx/nginx.conf" << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    include /etc/nginx/sites-available/*;
}
EOF

# Create PHP configuration
cat > "$PACKAGE_DIR/deployment/php/php.ini" << 'EOF'
[PHP]
post_max_size = 100M
upload_max_filesize = 100M
memory_limit = 256M
max_execution_time = 300
max_input_vars = 3000

[opcache]
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
EOF

# Create supervisor config
cat > "$PACKAGE_DIR/deployment/supervisor/supervisord.conf" << 'EOF'
[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:php-fpm]
command=php-fpm
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/php-fpm.err.log
stdout_logfile=/var/log/supervisor/php-fpm.out.log

[program:laravel-queue]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
user=www
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/supervisor/laravel-queue.log
EOF

# Create backup script
cat > "$PACKAGE_DIR/scripts/backup.sh" << 'EOF'
#!/bin/bash
# Database backup script

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/isp_backup_$DATE.sql"

echo "Creating database backup: $BACKUP_FILE"
pg_dump -h $POSTGRES_HOST -U $POSTGRES_USER -d $POSTGRES_DB > $BACKUP_FILE

# Compress the backup
gzip $BACKUP_FILE

# Keep only last 7 days of backups
find $BACKUP_DIR -name "isp_backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: ${BACKUP_FILE}.gz"
EOF

chmod +x "$PACKAGE_DIR/scripts/backup.sh"

# Clean up storage directories
print_status "Cleaning up storage directories..."
rm -rf "$PACKAGE_DIR/storage/logs/*" 2>/dev/null || true
rm -rf "$PACKAGE_DIR/storage/framework/cache/*" 2>/dev/null || true
rm -rf "$PACKAGE_DIR/storage/framework/sessions/*" 2>/dev/null || true
rm -rf "$PACKAGE_DIR/storage/framework/views/*" 2>/dev/null || true

# Create .gitkeep files for empty directories
touch "$PACKAGE_DIR/storage/logs/.gitkeep"
touch "$PACKAGE_DIR/storage/framework/cache/.gitkeep"
touch "$PACKAGE_DIR/storage/framework/sessions/.gitkeep"
touch "$PACKAGE_DIR/storage/framework/views/.gitkeep"

# Create client-specific README
cat > "$PACKAGE_DIR/README-CLIENT.md" << EOF
# ISP Management System - ${CLIENT_NAME}

Welcome to your ISP Management System package!

## Quick Start

1. **Upload to your server**:
   \`\`\`bash
   scp -r isp-management-${VERSION}/ user@your-server:/opt/
   \`\`\`

2. **Run installation**:
   \`\`\`bash
   cd /opt/isp-management-${VERSION}/
   chmod +x install.sh
   ./install.sh
   \`\`\`

3. **Follow the prompts** to configure your system

## Package Information
- **Version**: ${VERSION}
- **Client**: ${CLIENT_NAME}
- **Package Date**: $(date)

## Support
- Documentation: See README.md
- Issues: Contact your provider
- Email: <EMAIL>

## Next Steps
1. Complete the installation
2. Configure your MikroTik devices
3. Set up your bandwidth plans
4. Import your customers
5. Start managing your ISP!
EOF

# Create version file
cat > "$PACKAGE_DIR/VERSION" << EOF
ISP Management System
Version: ${VERSION}
Client: ${CLIENT_NAME}
Package Date: $(date)
EOF

# Create archive
print_status "Creating package archive..."
tar -czf "${PACKAGE_DIR}.tar.gz" "$PACKAGE_DIR/"

# Calculate file size
SIZE=$(du -h "${PACKAGE_DIR}.tar.gz" | cut -f1)

echo ""
echo "🎉 Package created successfully!"
echo "================================"
echo ""
echo "📦 Package: ${PACKAGE_DIR}.tar.gz"
echo "📏 Size: $SIZE"
echo "👤 Client: $CLIENT_NAME"
echo "🏷️  Version: $VERSION"
echo ""
echo "📋 Next Steps:"
echo "1. Send the package to your client"
echo "2. Provide installation instructions"
echo "3. Offer setup support if needed"
echo ""
echo "📧 Client Instructions:"
echo "1. Extract: tar -xzf ${PACKAGE_DIR}.tar.gz"
echo "2. Install: cd ${PACKAGE_DIR} && ./install.sh"
echo "3. Configure: Follow the installation prompts"
echo ""
