# 🔌 ISP Management System - API Documentation

This guide covers the REST API for integrating with external systems, mobile applications, and third-party services.

## 🚀 **API Overview**

### **Base Information**
- **Base URL**: `https://your-domain.com/api/v1`
- **Authentication**: Bear<PERSON>ken
- **Content Type**: `application/json`
- **Rate Limiting**: 60 requests per minute per API key
- **Versioning**: URL-based versioning (`/api/v1/`)

### **Response Format**
All API responses follow a consistent format:

```json
{
    "success": true,
    "data": {
        // Response data here
    },
    "message": "Operation completed successfully",
    "meta": {
        "timestamp": "2025-01-28T14:30:22Z",
        "version": "v1.0.0"
    }
}
```

### **Error Response Format**
```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "The given data was invalid.",
        "details": {
            "email": ["The email field is required."]
        }
    },
    "meta": {
        "timestamp": "2025-01-28T14:30:22Z",
        "version": "v1.0.0"
    }
}
```

## 🔐 **Authentication**

### **API Key Generation**
1. Navigate to **Admin Panel → API → Keys**
2. Click **Generate New Key**
3. Set permissions and expiration
4. Copy the generated token

### **Authentication Header**
Include the API key in all requests:
```http
Authorization: Bearer your-api-key-here
Content-Type: application/json
```

### **Example Request**
```bash
curl -X GET "https://your-domain.com/api/v1/customers" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json"
```

## 👥 **Customer Management**

### **List Customers**
```http
GET /api/v1/customers
```

**Parameters:**
- `page` (integer): Page number (default: 1)
- `per_page` (integer): Items per page (default: 15, max: 100)
- `search` (string): Search by name, email, or phone
- `status` (string): Filter by status (active, suspended, cancelled)
- `plan_id` (integer): Filter by bandwidth plan

**Response:**
```json
{
    "success": true,
    "data": {
        "customers": [
            {
                "id": 1,
                "name": "John Doe",
                "email": "<EMAIL>",
                "phone": "+254700123456",
                "status": "active",
                "service_type": "pppoe",
                "bandwidth_plan": {
                    "id": 2,
                    "name": "Home Basic",
                    "download_speed": "10 Mbps",
                    "upload_speed": "2 Mbps"
                },
                "created_at": "2025-01-15T10:30:00Z",
                "updated_at": "2025-01-28T14:30:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_items": 67,
            "per_page": 15
        }
    }
}
```

### **Get Customer Details**
```http
GET /api/v1/customers/{id}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "customer": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "+254700123456",
            "address": "123 Main Street, Nairobi",
            "status": "active",
            "service_type": "pppoe",
            "username": "john.doe",
            "ip_address": "**************",
            "bandwidth_plan": {
                "id": 2,
                "name": "Home Basic",
                "price": 2000,
                "download_speed": "10 Mbps",
                "upload_speed": "2 Mbps"
            },
            "billing": {
                "monthly_fee": 2000,
                "next_billing_date": "2025-02-15",
                "payment_status": "current"
            },
            "usage": {
                "current_month_download": "45.2 GB",
                "current_month_upload": "8.7 GB",
                "last_online": "2025-01-28T14:25:00Z"
            }
        }
    }
}
```

### **Create Customer**
```http
POST /api/v1/customers
```

**Request Body:**
```json
{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "+************",
    "address": "456 Oak Avenue, Mombasa",
    "service_type": "static_ip",
    "bandwidth_plan_id": 3,
    "installation_date": "2025-02-01"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "customer": {
            "id": 68,
            "name": "Jane Smith",
            "email": "<EMAIL>",
            "account_number": "ACC-********-068",
            "service_credentials": {
                "ip_address": "**************",
                "gateway": "*************",
                "dns_servers": ["*******", "*******"]
            }
        }
    },
    "message": "Customer created successfully"
}
```

### **Update Customer**
```http
PUT /api/v1/customers/{id}
```

### **Suspend/Activate Customer**
```http
POST /api/v1/customers/{id}/suspend
POST /api/v1/customers/{id}/activate
```

## 💰 **Billing Management**

### **List Invoices**
```http
GET /api/v1/invoices
```

**Parameters:**
- `customer_id` (integer): Filter by customer
- `status` (string): paid, unpaid, overdue
- `date_from` (date): Start date filter
- `date_to` (date): End date filter

### **Get Invoice Details**
```http
GET /api/v1/invoices/{id}
```

### **Create Invoice**
```http
POST /api/v1/invoices
```

**Request Body:**
```json
{
    "customer_id": 1,
    "due_date": "2025-02-15",
    "items": [
        {
            "description": "Monthly Internet Service",
            "amount": 2000,
            "quantity": 1
        },
        {
            "description": "Router Rental",
            "amount": 500,
            "quantity": 1
        }
    ]
}
```

### **Record Payment**
```http
POST /api/v1/payments
```

**Request Body:**
```json
{
    "invoice_id": 123,
    "amount": 2500,
    "payment_method": "mpesa",
    "reference": "MPX123456789",
    "payment_date": "2025-01-28"
}
```

## 🌐 **Network Management**

### **List Network Devices**
```http
GET /api/v1/network/devices
```

### **Get Device Status**
```http
GET /api/v1/network/devices/{id}/status
```

**Response:**
```json
{
    "success": true,
    "data": {
        "device": {
            "id": 1,
            "name": "Router-Main-01",
            "ip_address": "***********",
            "status": "online",
            "uptime": "15 days, 8 hours",
            "cpu_usage": "12%",
            "memory_usage": "45%",
            "interfaces": [
                {
                    "name": "ether1",
                    "status": "up",
                    "rx_bytes": "1.2 TB",
                    "tx_bytes": "890 GB"
                }
            ]
        }
    }
}
```

### **List IP Pools**
```http
GET /api/v1/network/ip-pools
```

### **Get Bandwidth Plans**
```http
GET /api/v1/bandwidth-plans
```

## 📊 **Reports and Analytics**

### **Revenue Report**
```http
GET /api/v1/reports/revenue
```

**Parameters:**
- `period` (string): daily, weekly, monthly, yearly
- `date_from` (date): Start date
- `date_to` (date): End date

### **Customer Statistics**
```http
GET /api/v1/reports/customers
```

### **Usage Statistics**
```http
GET /api/v1/reports/usage
```

**Response:**
```json
{
    "success": true,
    "data": {
        "usage_stats": {
            "total_customers": 150,
            "active_customers": 142,
            "suspended_customers": 8,
            "total_bandwidth_allocated": "2.1 Gbps",
            "peak_usage": {
                "download": "1.8 Gbps",
                "upload": "450 Mbps",
                "time": "2025-01-28T20:30:00Z"
            },
            "top_users": [
                {
                    "customer_id": 45,
                    "name": "Business Customer A",
                    "usage": "125 GB"
                }
            ]
        }
    }
}
```

## 🎫 **Support Tickets**

### **List Tickets**
```http
GET /api/v1/tickets
```

### **Create Ticket**
```http
POST /api/v1/tickets
```

**Request Body:**
```json
{
    "customer_id": 1,
    "subject": "Internet connection slow",
    "description": "Customer reports slow internet speeds since yesterday",
    "priority": "medium",
    "category": "technical"
}
```

### **Update Ticket**
```http
PUT /api/v1/tickets/{id}
```

## 📱 **Mobile App Endpoints**

### **Customer Login**
```http
POST /api/v1/mobile/login
```

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "customer-password"
}
```

### **Customer Dashboard**
```http
GET /api/v1/mobile/dashboard
```

### **Usage Statistics**
```http
GET /api/v1/mobile/usage
```

### **Payment History**
```http
GET /api/v1/mobile/payments
```

## 🔧 **Webhooks**

### **Payment Webhooks**
Configure webhooks for payment notifications:

```http
POST /api/v1/webhooks/payments
```

**Webhook Payload:**
```json
{
    "event": "payment.received",
    "data": {
        "payment_id": 456,
        "customer_id": 1,
        "amount": 2000,
        "method": "mpesa",
        "reference": "MPX123456789"
    },
    "timestamp": "2025-01-28T14:30:22Z"
}
```

## 📋 **Error Codes**

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `AUTHENTICATION_ERROR` | Invalid or missing API key |
| `AUTHORIZATION_ERROR` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `SERVER_ERROR` | Internal server error |

## 🔒 **Security**

### **API Key Security**
- Store API keys securely
- Use HTTPS for all requests
- Rotate keys regularly
- Monitor API usage

### **Rate Limiting**
- 60 requests per minute per API key
- Burst allowance: 10 requests
- Rate limit headers included in responses

### **IP Whitelisting**
Configure IP restrictions in API key settings:
```
Allowed IPs: ***********/24, *************
```

## 📚 **SDKs and Libraries**

### **PHP SDK**
```php
use ISPManagement\SDK\Client;

$client = new Client('your-api-key', 'https://your-domain.com');
$customers = $client->customers()->list();
```

### **JavaScript SDK**
```javascript
import ISPClient from 'isp-management-sdk';

const client = new ISPClient('your-api-key', 'https://your-domain.com');
const customers = await client.customers.list();
```

## 🧪 **Testing**

### **Postman Collection**
Download the Postman collection: `/api/v1/postman-collection`

### **API Testing**
```bash
# Test authentication
curl -X GET "https://your-domain.com/api/v1/auth/test" \
  -H "Authorization: Bearer your-api-key"

# Test customer creation
curl -X POST "https://your-domain.com/api/v1/customers" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Customer","email":"<EMAIL>"}'
```

---

**API Documentation Complete!** 🔌

The ISP Management System API provides comprehensive access to all system functionality for building integrations, mobile apps, and custom solutions.
