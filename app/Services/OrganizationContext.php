<?php

namespace App\Services;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class OrganizationContext
{
    protected ?Organization $organization = null;
    protected bool $loaded = false;

    /**
     * Get the current organization.
     */
    public function getOrganization(): ?Organization
    {
        if (!$this->loaded) {
            $this->loadOrganization();
        }

        return $this->organization;
    }

    /**
     * Get the current organization ID.
     */
    public function getId(): ?int
    {
        $organization = $this->getOrganization();
        return $organization ? $organization->id : null;
    }

    /**
     * Check if there is a current organization.
     */
    public function hasOrganization(): bool
    {
        return $this->getOrganization() !== null;
    }

    /**
     * Set the current organization.
     */
    public function setOrganization(?Organization $organization): void
    {
        $this->organization = $organization;
        $this->loaded = true;
    }

    /**
     * Set the current organization by ID.
     */
    public function setOrganizationById(?int $organizationId): void
    {
        if ($organizationId) {
            $organization = Organization::find($organizationId);
            $this->setOrganization($organization);
        } else {
            $this->setOrganization(null);
        }
    }

    /**
     * Clear the current organization.
     */
    public function clear(): void
    {
        $this->organization = null;
        $this->loaded = true;
    }

    /**
     * Load the organization from the authenticated user.
     */
    protected function loadOrganization(): void
    {
        $this->loaded = true;

        if (!Auth::check()) {
            $this->organization = null;
            return;
        }

        $user = Auth::user();
        
        if ($user && $user->organization_id) {
            $this->organization = $user->organization;
        } else {
            $this->organization = null;
        }
    }

    /**
     * Switch to a different organization (if user has access).
     */
    public function switchToOrganization(int $organizationId): bool
    {
        if (!Auth::check()) {
            return false;
        }

        $user = Auth::user();
        
        // Check if user belongs to the requested organization
        if ($user->organization_id !== $organizationId) {
            return false;
        }

        $organization = Organization::find($organizationId);
        
        if (!$organization || !$organization->isActive()) {
            return false;
        }

        $this->setOrganization($organization);
        return true;
    }

    /**
     * Execute a callback without organization scoping.
     * Use with extreme caution - only for system operations.
     */
    public function withoutOrganization(callable $callback)
    {
        $originalOrganization = $this->organization;
        $originalLoaded = $this->loaded;

        try {
            $this->organization = null;
            $this->loaded = true;

            return $callback();
        } finally {
            $this->organization = $originalOrganization;
            $this->loaded = $originalLoaded;
        }
    }

    /**
     * Execute a callback with a specific organization context.
     */
    public function withOrganization(Organization $organization, callable $callback)
    {
        $originalOrganization = $this->organization;
        $originalLoaded = $this->loaded;

        try {
            $this->setOrganization($organization);

            return $callback();
        } finally {
            $this->organization = $originalOrganization;
            $this->loaded = $originalLoaded;
        }
    }

    /**
     * Get organization name for display.
     */
    public function getName(): ?string
    {
        $organization = $this->getOrganization();
        return $organization ? $organization->name : null;
    }

    /**
     * Get organization slug.
     */
    public function getSlug(): ?string
    {
        $organization = $this->getOrganization();
        return $organization ? $organization->slug : null;
    }

    /**
     * Check if the current organization is active.
     */
    public function isActive(): bool
    {
        $organization = $this->getOrganization();
        return $organization ? $organization->isActive() : false;
    }
}
