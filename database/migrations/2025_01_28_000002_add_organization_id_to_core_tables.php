<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add organization_id to customers table
        Schema::table('customers', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });

        // Add organization_id to subscriptions table
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });

        // Add organization_id to invoices table
        Schema::table('invoices', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });

        // Add organization_id to invoice_items table
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });

        // Add organization_id to payments table
        Schema::table('payments', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });

        // Add organization_id to bandwidth_plans table
        Schema::table('bandwidth_plans', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });

        // Add organization_id to network_sites table
        Schema::table('network_sites', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });

        // Add organization_id to network_devices table
        Schema::table('network_devices', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });

        // Add organization_id to ip_pools table
        Schema::table('ip_pools', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });

        // Add organization_id to static_ip_services table
        Schema::table('static_ip_services', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });

        // Add organization_id to pppoe_services table
        Schema::table('pppoe_services', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->index(['organization_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $tables = [
            'pppoe_services',
            'static_ip_services', 
            'ip_pools',
            'network_devices',
            'network_sites',
            'bandwidth_plans',
            'payments',
            'invoice_items',
            'invoices',
            'subscriptions',
            'customers'
        ];

        foreach ($tables as $table) {
            Schema::table($table, function (Blueprint $tableSchema) {
                $tableSchema->dropForeign(['organization_id']);
                $tableSchema->dropIndex(['organization_id']);
                $tableSchema->dropColumn('organization_id');
            });
        }
    }
};
