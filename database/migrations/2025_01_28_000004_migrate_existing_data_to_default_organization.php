<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create a default organization
        $organizationId = DB::table('organizations')->insertGetId([
            'name' => 'Default Organization',
            'slug' => 'default',
            'description' => 'Default organization for existing data migration',
            'status' => 'active',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Update all existing records to belong to the default organization
        $tables = [
            'users',
            'customers',
            'subscriptions',
            'invoices',
            'invoice_items',
            'payments',
            'bandwidth_plans',
            'network_sites',
            'network_devices',
            'ip_pools',
            'static_ip_services',
            'pppoe_services',
        ];

        foreach ($tables as $table) {
            if (Schema::hasTable($table) && Schema::hasColumn($table, 'organization_id')) {
                DB::table($table)->update(['organization_id' => $organizationId]);
            }
        }

        // Update additional tables if they exist
        $additionalTables = [
            'bandwidth_usage',
            'bandwidth_assignments',
            'bandwidth_policies',
            'bandwidth_quotas',
            'bandwidth_rules',
            'network_interfaces',
            'network_connections',
            'network_maps',
            'network_map_items',
            'ip_addresses',
            'queue_usage',
        ];

        foreach ($additionalTables as $table) {
            if (Schema::hasTable($table) && Schema::hasColumn($table, 'organization_id')) {
                DB::table($table)->update(['organization_id' => $organizationId]);
            }
        }

        // Make organization_id NOT NULL after data migration
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('customers', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('subscriptions', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('invoice_items', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('payments', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('bandwidth_plans', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('network_sites', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('network_devices', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('ip_pools', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('static_ip_services', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });

        Schema::table('pppoe_services', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Make organization_id nullable again
        $tables = [
            'pppoe_services',
            'static_ip_services',
            'ip_pools',
            'network_devices',
            'network_sites',
            'bandwidth_plans',
            'payments',
            'invoice_items',
            'invoices',
            'subscriptions',
            'customers',
            'users'
        ];

        foreach ($tables as $table) {
            if (Schema::hasTable($table) && Schema::hasColumn($table, 'organization_id')) {
                Schema::table($table, function (Blueprint $tableSchema) {
                    $tableSchema->foreignId('organization_id')->nullable()->change();
                });
            }
        }

        // Remove the default organization
        DB::table('organizations')->where('slug', 'default')->delete();
    }
};
