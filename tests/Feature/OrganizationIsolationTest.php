<?php

namespace Tests\Feature;

use App\Http\Controllers\DashboardController;
use App\Models\Customer;
use App\Models\Organization;
use App\Services\OrganizationContext;
use App\Services\QueryOptimizationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrganizationIsolationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that dashboard shows only organization-specific data.
     */
    public function test_dashboard_shows_only_organization_data()
    {
        // Create two organizations
        $org1 = Organization::factory()->create(['name' => 'Organization 1']);
        $org2 = Organization::factory()->create(['name' => 'Organization 2']);

        // Create customers for each organization
        $org1Customer = Customer::factory()->create([
            'organization_id' => $org1->id,
            'name' => 'Customer Org 1'
        ]);
        $org2Customer = Customer::factory()->create([
            'organization_id' => $org2->id,
            'name' => 'Customer Org 2'
        ]);

        // Set organization context to org1
        app(OrganizationContext::class)->setOrganizationById($org1->id);

        // Test that Customer model queries are scoped to org1
        $customers = Customer::all();
        $this->assertCount(1, $customers);
        $this->assertEquals($org1Customer->id, $customers->first()->id);
        $this->assertEquals('Customer Org 1', $customers->first()->name);

        // Switch to org2 context
        app(OrganizationContext::class)->setOrganizationById($org2->id);

        // Test that Customer model queries are now scoped to org2
        $customers = Customer::all();
        $this->assertCount(1, $customers);
        $this->assertEquals($org2Customer->id, $customers->first()->id);
        $this->assertEquals('Customer Org 2', $customers->first()->name);
    }

    /**
     * Test that QueryOptimizationService respects organization boundaries.
     */
    public function test_query_optimization_service_respects_organization_boundaries()
    {
        // Create two organizations with customers
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        Customer::factory()->count(3)->create(['organization_id' => $org1->id]);
        Customer::factory()->count(5)->create(['organization_id' => $org2->id]);

        // Set organization context to org1
        app(OrganizationContext::class)->setOrganizationById($org1->id);

        // Test dashboard stats only show org1 data
        $stats = QueryOptimizationService::getDashboardStats();
        $this->assertEquals(3, $stats['customers']['total']);

        // Switch to org2 context
        app(OrganizationContext::class)->setOrganizationById($org2->id);

        // Test dashboard stats now show org2 data
        $stats = QueryOptimizationService::getDashboardStats();
        $this->assertEquals(5, $stats['customers']['total']);
    }

    /**
     * Test that QueryOptimizationService throws exception without organization context.
     */
    public function test_query_optimization_service_requires_organization_context()
    {
        // Clear organization context
        app(OrganizationContext::class)->clear();

        // Should throw exception when no organization context
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('No organization context available for dashboard stats');

        QueryOptimizationService::getDashboardStats();
    }

    /**
     * Test that different organization contexts don't see each other's data.
     */
    public function test_organization_data_isolation()
    {
        // Create organizations with different data
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        // Create different amounts of customers for each org
        Customer::factory()->count(10)->create(['organization_id' => $org1->id]);
        Customer::factory()->count(20)->create(['organization_id' => $org2->id]);

        // Test org1 context
        app(OrganizationContext::class)->setOrganizationById($org1->id);
        $this->assertEquals(10, Customer::count());

        // Test org2 context
        app(OrganizationContext::class)->setOrganizationById($org2->id);
        $this->assertEquals(20, Customer::count());

        // Verify no cross-contamination
        app(OrganizationContext::class)->setOrganizationById($org1->id);
        $org1Customers = Customer::all();
        foreach ($org1Customers as $customer) {
            $this->assertEquals($org1->id, $customer->organization_id);
        }

        app(OrganizationContext::class)->setOrganizationById($org2->id);
        $org2Customers = Customer::all();
        foreach ($org2Customers as $customer) {
            $this->assertEquals($org2->id, $customer->organization_id);
        }
    }
}
