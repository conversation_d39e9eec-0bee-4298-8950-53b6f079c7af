<?php

namespace App\Services\Import\Data;

use App\Services\Import\Contracts\ImportableDataInterface;
use Illuminate\Support\Facades\Validator;

/**
 * Customer import data structure
 * Maps external customer data to our platform format
 */
class CustomerImportData implements ImportableDataInterface
{
    protected array $customers = [];

    protected array $validationErrors = [];

    protected bool $validated = false;

    public function __construct(array $customers = [])
    {
        $this->customers = $customers;
    }

    /**
     * Add a customer to the import data
     */
    public function addCustomer(array $customerData): self
    {
        $this->customers[] = $this->normalizeCustomerData($customerData);
        $this->validated = false;

        return $this;
    }

    /**
     * Add multiple customers
     */
    public function addCustomers(array $customers): self
    {
        foreach ($customers as $customer) {
            $this->addCustomer($customer);
        }

        return $this;
    }

    /**
     * Normalize customer data to standard format
     */
    protected function normalizeCustomerData(array $data): array
    {
        return [
            // Core customer fields
            'name' => $data['name'] ?? $data['customer_name'] ?? '',
            'email' => $data['email'] ?? $data['customer_email'] ?? '',
            'phone' => $data['phone'] ?? $data['customer_phone'] ?? '',
            'address' => $data['address'] ?? $data['customer_address'] ?? '',
            'city' => $data['city'] ?? $data['customer_city'] ?? '',
            'state' => $data['state'] ?? $data['customer_state'] ?? '',
            'postal_code' => $data['postal_code'] ?? $data['zip_code'] ?? $data['customer_zip'] ?? '',
            'country' => $data['country'] ?? $data['customer_country'] ?? 'Kenya',
            'status' => $data['status'] ?? $data['customer_status'] ?? 'active',

            // Service information
            'service_type' => $data['service_type'] ?? 'static_ip', // static_ip or pppoe
            'bandwidth_plan_id' => $data['bandwidth_plan_id'] ?? $data['plan_id'] ?? 1,
            'network_site_id' => $data['network_site_id'] ?? $data['site_id'] ?? null,
            'device_id' => $data['device_id'] ?? null,

            // Service-specific data
            'ip_address' => $data['ip_address'] ?? null, // For static IP
            'ip_pool_id' => $data['ip_pool_id'] ?? $data['pool_id'] ?? null, // FIXED: Optional - will auto-assign from organization
            'username' => $data['username'] ?? $data['pppoe_username'] ?? null, // For PPPoE
            'password' => $data['password'] ?? $data['pppoe_password'] ?? null,

            // Billing information
            'subscription_price' => $data['subscription_price'] ?? $data['monthly_fee'] ?? null,
            'billing_cycle' => $data['billing_cycle'] ?? 'monthly',
            'start_date' => $data['start_date'] ?? $data['service_start'] ?? now()->toDateString(),
            'next_billing_date' => $data['next_billing_date'] ?? $data['next_bill'] ?? now()->addMonth()->toDateString(),

            // External platform mapping
            'external_id' => $data['external_id'] ?? $data['splynx_id'] ?? $data['source_id'] ?? null,
            'external_platform' => $data['external_platform'] ?? 'manual',

            // Additional metadata
            'notes' => $data['notes'] ?? $data['customer_notes'] ?? '',
            'import_batch' => $data['import_batch'] ?? null,
        ];
    }

    public function getData(): array
    {
        return $this->customers;
    }

    public function validate(): bool
    {
        if ($this->validated) {
            return empty($this->validationErrors);
        }

        $this->validationErrors = [];

        foreach ($this->customers as $index => $customer) {
            $validator = Validator::make($customer, [
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'service_type' => 'required|in:static_ip,pppoe',
                'bandwidth_plan_id' => 'required|integer|exists:bandwidth_plans,id',
                'network_site_id' => 'nullable|integer|exists:network_sites,id',
                'device_id' => 'nullable|integer|exists:network_devices,id',
                // For import scenarios, IP addresses and pools will be assigned during processing
                'ip_address' => 'nullable|ip',
                'ip_pool_id' => 'nullable|integer',
                // For import scenarios, PPPoE credentials can be generated during processing
                'username' => 'nullable|string|max:50',
                'password' => 'nullable|string|min:6',
                'subscription_price' => 'nullable|numeric|min:0',
                'billing_cycle' => 'in:monthly,quarterly,yearly',
                'start_date' => 'date',
                'next_billing_date' => 'date|after_or_equal:start_date',
            ]);

            if ($validator->fails()) {
                $this->validationErrors["customer_{$index}"] = $validator->errors()->toArray();
            }
        }

        $this->validated = true;

        return empty($this->validationErrors);
    }

    public function getValidationErrors(): array
    {
        return $this->validationErrors;
    }

    public function getRecordCount(): int
    {
        return count($this->customers);
    }

    public function getDataInChunks(int $chunkSize = 100): \Generator
    {
        $chunks = array_chunk($this->customers, $chunkSize);
        foreach ($chunks as $chunk) {
            yield $chunk;
        }
    }

    /**
     * Create from array (useful for Excel/CSV imports later)
     */
    public static function fromArray(array $data): self
    {
        return new self($data);
    }

    /**
     * Create from external platform data (e.g., Splynx API response)
     */
    public static function fromExternalPlatform(array $data, string $platform = 'splynx'): self
    {
        $customers = [];

        foreach ($data as $record) {
            $customers[] = array_merge($record, [
                'external_platform' => $platform,
                'external_id' => $record['id'] ?? null,
            ]);
        }

        return new self($customers);
    }
}
