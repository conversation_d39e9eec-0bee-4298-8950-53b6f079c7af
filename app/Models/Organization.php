<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Organization extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'settings',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'settings' => 'array',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($organization) {
            if (empty($organization->slug)) {
                $organization->slug = Str::slug($organization->name);
            }
        });

        static::updating(function ($organization) {
            if ($organization->isDirty('name') && empty($organization->slug)) {
                $organization->slug = Str::slug($organization->name);
            }
        });
    }

    /**
     * Get the users for the organization.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the customers for the organization.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Get the subscriptions for the organization.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the invoices for the organization.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the payments for the organization.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the bandwidth plans for the organization.
     */
    public function bandwidthPlans(): HasMany
    {
        return $this->hasMany(\App\Models\Bandwidth\BandwidthPlan::class);
    }

    /**
     * Get the network sites for the organization.
     */
    public function networkSites(): HasMany
    {
        return $this->hasMany(\App\Models\Network\NetworkSite::class);
    }

    /**
     * Get the network devices for the organization.
     */
    public function networkDevices(): HasMany
    {
        return $this->hasMany(\App\Models\Network\NetworkDevice::class);
    }

    /**
     * Get the IP pools for the organization.
     */
    public function ipPools(): HasMany
    {
        return $this->hasMany(\App\Models\Services\IpPool::class);
    }

    /**
     * Get the static IP services for the organization.
     */
    public function staticIpServices(): HasMany
    {
        return $this->hasMany(\App\Models\Services\StaticIpService::class);
    }

    /**
     * Get the PPPoE services for the organization.
     */
    public function pppoeServices(): HasMany
    {
        return $this->hasMany(\App\Models\Services\PppoeService::class);
    }

    /**
     * Check if the organization is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Scope a query to only include active organizations.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
