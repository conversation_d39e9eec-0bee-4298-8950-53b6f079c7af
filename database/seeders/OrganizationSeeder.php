<?php

namespace Database\Seeders;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class OrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample organizations
        $organizations = [
            [
                'name' => 'Acme ISP',
                'slug' => 'acme-isp',
                'description' => 'A leading internet service provider',
                'status' => 'active',
            ],
            [
                'name' => 'TechNet Solutions',
                'slug' => 'technet-solutions',
                'description' => 'Technology and networking solutions provider',
                'status' => 'active',
            ],
            [
                'name' => 'Global Connect',
                'slug' => 'global-connect',
                'description' => 'International connectivity services',
                'status' => 'active',
            ],
        ];

        foreach ($organizations as $orgData) {
            $organization = Organization::create($orgData);

            // Create an admin user for each organization
            User::create([
                'name' => $organization->name . ' Admin',
                'email' => 'admin@' . $organization->slug . '.com',
                'password' => Hash::make('password'),
                'organization_id' => $organization->id,
                'email_verified_at' => now(),
            ]);

            $this->command->info("Created organization: {$organization->name} with admin user");
        }

        // Update the default organization if it exists
        $defaultOrg = Organization::where('slug', 'default')->first();
        if ($defaultOrg) {
            // Create an admin user for the default organization if none exists
            $defaultAdmin = User::where('organization_id', $defaultOrg->id)->first();
            if (!$defaultAdmin) {
                User::create([
                    'name' => 'Default Admin',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'organization_id' => $defaultOrg->id,
                    'email_verified_at' => now(),
                ]);
                $this->command->info("Created admin user for default organization");
            }
        }
    }
}
