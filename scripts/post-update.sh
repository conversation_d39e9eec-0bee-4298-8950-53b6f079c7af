#!/bin/bash

# ISP Management System - Post-Update Script
# This script runs after each update to handle feature-specific setup

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[POST-UPDATE]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[POST-UPDATE]${NC} $1"; }
print_header() { echo -e "${BLUE}$1${NC}"; }

print_header "🔧 Running Post-Update Tasks"
print_header "============================"

# Get current version
VERSION_FILE="./VERSION"
CURRENT_VERSION="unknown"
if [[ -f "$VERSION_FILE" ]]; then
    CURRENT_VERSION=$(grep "Version:" "$VERSION_FILE" | cut -d' ' -f2 || echo "unknown")
fi

print_status "Processing post-update tasks for version: $CURRENT_VERSION"

# Function to run version-specific updates
run_version_specific_updates() {
    local version=$1
    
    case $version in
        "1.1.0"|"v1.1.0")
            print_status "Setting up accounting module (v1.1.0)..."
            
            # Create accounting tables if they don't exist
            if command -v docker-compose &> /dev/null; then
                docker-compose run --rm app php artisan migrate --path=database/migrations/accounting --force
            else
                php artisan migrate --path=database/migrations/accounting --force
            fi
            
            # Seed accounting data
            if command -v docker-compose &> /dev/null; then
                docker-compose run --rm app php artisan db:seed --class=AccountingSeeder --force
            else
                php artisan db:seed --class=AccountingSeeder --force
            fi
            
            print_status "✅ Accounting module setup complete"
            ;;
            
        "1.2.0"|"v1.2.0")
            print_status "Setting up advanced reporting (v1.2.0)..."
            
            # Create reports directory
            mkdir -p storage/app/reports
            chmod 755 storage/app/reports
            
            # Install reporting dependencies
            if command -v docker-compose &> /dev/null; then
                docker-compose run --rm app composer require --no-dev maatwebsite/excel
            else
                composer require --no-dev maatwebsite/excel
            fi
            
            print_status "✅ Advanced reporting setup complete"
            ;;
            
        "2.0.0"|"v2.0.0")
            print_status "Setting up API v2 (v2.0.0)..."
            
            # Generate API documentation
            if command -v docker-compose &> /dev/null; then
                docker-compose run --rm app php artisan l5-swagger:generate
            else
                php artisan l5-swagger:generate
            fi
            
            # Create API keys table
            if command -v docker-compose &> /dev/null; then
                docker-compose run --rm app php artisan migrate --path=database/migrations/api --force
            else
                php artisan migrate --path=database/migrations/api --force
            fi
            
            print_status "✅ API v2 setup complete"
            ;;
    esac
}

# Function to update custom configurations
update_custom_configs() {
    print_status "Updating custom configurations..."
    
    # Check if custom config overrides exist
    if [[ -d "custom/config" ]]; then
        print_status "Applying custom configuration overrides..."
        
        # Copy custom configs (they override default ones)
        for config_file in custom/config/*.php; do
            if [[ -f "$config_file" ]]; then
                filename=$(basename "$config_file")
                cp "$config_file" "config/$filename"
                print_status "Applied custom config: $filename"
            fi
        done
    fi
    
    # Update config cache
    if command -v docker-compose &> /dev/null; then
        docker-compose run --rm app php artisan config:cache
    else
        php artisan config:cache
    fi
}

# Function to update custom views
update_custom_views() {
    print_status "Updating custom views..."
    
    if [[ -d "custom/views" ]]; then
        print_status "Applying custom view overrides..."
        
        # Create views directory structure if it doesn't exist
        mkdir -p resources/views/custom
        
        # Copy custom views
        cp -r custom/views/* resources/views/ 2>/dev/null || true
        
        print_status "✅ Custom views applied"
    fi
}

# Function to update custom assets
update_custom_assets() {
    print_status "Updating custom assets..."
    
    if [[ -d "custom/assets" ]]; then
        print_status "Applying custom assets..."
        
        # Copy custom CSS
        if [[ -d "custom/assets/css" ]]; then
            mkdir -p public/css/custom
            cp -r custom/assets/css/* public/css/custom/ 2>/dev/null || true
        fi
        
        # Copy custom JS
        if [[ -d "custom/assets/js" ]]; then
            mkdir -p public/js/custom
            cp -r custom/assets/js/* public/js/custom/ 2>/dev/null || true
        fi
        
        # Copy custom images
        if [[ -d "custom/assets/images" ]]; then
            mkdir -p public/images/custom
            cp -r custom/assets/images/* public/images/custom/ 2>/dev/null || true
        fi
        
        print_status "✅ Custom assets applied"
    fi
}

# Function to run custom scripts
run_custom_scripts() {
    print_status "Running custom scripts..."
    
    if [[ -d "custom/scripts" ]]; then
        for script in custom/scripts/*.sh; do
            if [[ -f "$script" && -x "$script" ]]; then
                print_status "Running custom script: $(basename "$script")"
                "$script" || print_warning "Custom script failed: $(basename "$script")"
            fi
        done
    fi
}

# Function to update permissions
update_permissions() {
    print_status "Updating file permissions..."
    
    # Ensure storage directories are writable
    chmod -R 755 storage/
    chmod -R 755 bootstrap/cache/
    
    # Ensure custom directories have correct permissions
    if [[ -d "custom" ]]; then
        chmod -R 755 custom/
    fi
    
    print_status "✅ Permissions updated"
}

# Function to validate installation
validate_installation() {
    print_status "Validating installation..."
    
    # Check if application can boot
    if command -v docker-compose &> /dev/null; then
        if docker-compose run --rm app php artisan --version >/dev/null 2>&1; then
            print_status "✅ Application boots successfully"
        else
            print_warning "⚠️ Application boot check failed"
        fi
    else
        if php artisan --version >/dev/null 2>&1; then
            print_status "✅ Application boots successfully"
        else
            print_warning "⚠️ Application boot check failed"
        fi
    fi
    
    # Check database connection
    if command -v docker-compose &> /dev/null; then
        if docker-compose run --rm app php artisan migrate:status >/dev/null 2>&1; then
            print_status "✅ Database connection successful"
        else
            print_warning "⚠️ Database connection check failed"
        fi
    else
        if php artisan migrate:status >/dev/null 2>&1; then
            print_status "✅ Database connection successful"
        else
            print_warning "⚠️ Database connection check failed"
        fi
    fi
}

# Main execution
print_status "Starting post-update tasks..."

# Run version-specific updates
run_version_specific_updates "$CURRENT_VERSION"

# Update custom configurations
update_custom_configs

# Update custom views
update_custom_views

# Update custom assets
update_custom_assets

# Update permissions
update_permissions

# Run custom scripts
run_custom_scripts

# Validate installation
validate_installation

print_status "🎉 Post-update tasks completed successfully!"

# Log completion
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Post-update tasks completed for version $CURRENT_VERSION" >> storage/logs/updates.log
