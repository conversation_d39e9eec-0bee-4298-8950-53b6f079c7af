<?php

namespace App\Jobs\Import;

use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Subscription;
use App\Services\ServiceProvisioningFactory;
use App\Services\ServiceResult;
use App\Services\ValidationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessCustomerImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600; // 10 minutes

    public $tries = 3;

    public $backoff = [60, 120, 300];

    protected array $customersData;

    protected string $batchId;

    protected int $organizationId;

    protected ServiceProvisioningFactory $serviceFactory;

    public function __construct(array $customersData, string $batchId, int $organizationId)
    {
        $this->customersData = $customersData;
        $this->batchId = $batchId;
        $this->organizationId = $organizationId;
        $this->serviceFactory = new ServiceProvisioningFactory;
    }

    public function handle(): void
    {
        // FIXED: Set organization context for the job so BelongsToOrganization trait works
        app(\App\Services\OrganizationContext::class)->setOrganizationById($this->organizationId);

        Log::info('Starting customer import batch', [
            'batch_id' => $this->batchId,
            'customers_count' => count($this->customersData),
            'organization_id' => $this->organizationId, // FIXED: Track organization in logs
        ]);

        $successCount = 0;
        $skippedCount = 0;
        $failureCount = 0;
        $errors = [];

        foreach ($this->customersData as $customerData) {
            DB::beginTransaction();

            try {
                // Sanitize input data
                $customerData = ValidationService::sanitizeData($customerData);

                // Validate import data
                $validation = ValidationService::validateImportData($customerData);
                if ($validation->isError()) {
                    DB::rollBack();
                    $failureCount++;
                    $errors[] = [
                        'customer_name' => $customerData['name'] ?? 'Unknown',
                        'error' => $validation->getMessage(),
                        'details' => $validation->getErrors(),
                    ];

                    continue;
                }

                $result = $this->createCustomerWithServices($customerData);

                if ($result->isSuccess()) {
                    DB::commit();

                    $data = $result->getData();
                    if (isset($data['skipped']) && $data['skipped']) {
                        $skippedCount++;
                        Log::info('Customer skipped (already exists)', [
                            'batch_id' => $this->batchId,
                            'customer_name' => $customerData['name'],
                            'customer_email' => $customerData['email'],
                        ]);
                    } else {
                        $successCount++;
                        Log::info('Customer created successfully', [
                            'batch_id' => $this->batchId,
                            'customer_name' => $customerData['name'],
                            'customer_id' => $data['customer_id'],
                            'service_type' => $customerData['service_type'],
                        ]);
                    }
                } else {
                    DB::rollBack();
                    $failureCount++;
                    $errors[] = [
                        'customer_name' => $customerData['name'] ?? 'Unknown',
                        'error' => $result->getMessage(),
                        'details' => $result->getErrors(),
                    ];
                }
            } catch (\Exception $e) {
                DB::rollBack();
                $failureCount++;
                $errors[] = [
                    'customer_name' => $customerData['name'] ?? 'Unknown',
                    'error' => $e->getMessage(),
                ];

                Log::error('Failed to create customer', [
                    'batch_id' => $this->batchId,
                    'customer_data' => $customerData,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        Log::info('Customer import batch completed', [
            'batch_id' => $this->batchId,
            'success_count' => $successCount,
            'skipped_count' => $skippedCount,
            'failure_count' => $failureCount,
            'errors' => $errors,
        ]);
    }

    protected function createCustomerWithServices(array $customerData): ServiceResult
    {
        try {
            // Check if customer already exists by email or name - FIXED: Organization-scoped via trait
            $existingCustomer = Customer::where(function($query) use ($customerData) {
                $query->where('email', $customerData['email'])
                      ->orWhere('name', $customerData['name']);
            })->first();

            if ($existingCustomer) {
                Log::info('Customer already exists, skipping', [
                    'customer_name' => $customerData['name'],
                    'customer_email' => $customerData['email'],
                    'existing_id' => $existingCustomer->id,
                ]);

                return ServiceResult::success([
                    'skipped' => true,
                    'customer_id' => $existingCustomer->id,
                ], 'Customer already exists, skipped');
            }

            // Create customer - FIXED: Now includes organization_id for multi-tenant compliance
            $customerCreateData = [
                'name' => $customerData['name'],
                'email' => $customerData['email'],
                'phone' => $customerData['phone'] ?? '',
                'address' => $customerData['address'] ?? '',
                'city' => $customerData['city'] ?? '',
                'state' => $customerData['state'] ?? '',
                'postal_code' => $customerData['postal_code'] ?? '',
                'country' => $customerData['country'] ?? 'Kenya',
                'status' => $customerData['status'] ?? 'active',
                'organization_id' => $this->organizationId, // FIXED: Multi-tenant organization assignment
            ];

            $customer = Customer::create($customerCreateData);

            // Get bandwidth plan
            $bandwidthPlan = BandwidthPlan::find($customerData['bandwidth_plan_id']);
            if (! $bandwidthPlan) {
                return ServiceResult::error('Bandwidth plan not found');
            }

            // Create subscription
            $subscription = Subscription::create([
                'customer_id' => $customer->id,
                'bandwidth_plan_id' => $bandwidthPlan->id,
                'network_site_id' => $customerData['network_site_id'],
                'name' => $bandwidthPlan->name,
                'description' => $bandwidthPlan->description,
                'price' => $customerData['subscription_price'] ?? $bandwidthPlan->price,
                'billing_cycle' => $customerData['billing_cycle'],
                'start_date' => $customerData['start_date'],
                'next_billing_date' => $customerData['next_billing_date'],
                'status' => 'active',
            ]);

            // Create service using unified factory
            $serviceResult = $this->createService($customer, $subscription, $customerData);
            if ($serviceResult->isError()) {
                return $serviceResult;
            }

            // Create initial invoice
            $this->createInitialInvoice($customer, $subscription);

            $serviceData = $serviceResult->getData();

            return ServiceResult::success([
                'customer_id' => $customer->id,
                'subscription_id' => $subscription->id,
                'service_id' => $serviceData['service']->id,
                'service_type' => $customerData['service_type'],
            ], 'Customer and service created successfully');

        } catch (\Exception $e) {
            return ServiceResult::error('Failed to create customer: '.$e->getMessage());
        }
    }

    protected function createService(Customer $customer, Subscription $subscription, array $customerData): ServiceResult
    {
        $config = [
            'device_id' => $customerData['device_id'],
            'bandwidth_plan_id' => $customerData['bandwidth_plan_id'],
            'comment' => "Imported customer: {$customer->name}",
        ];

        if ($customerData['service_type'] === 'static_ip') {
            $config['ip_pool_id'] = $customerData['ip_pool_id'];
            if (isset($customerData['ip_address']) && ! empty($customerData['ip_address'])) {
                $config['ip_address'] = $customerData['ip_address'];
            }

            return $this->serviceFactory->createStaticIpService($customer, $subscription, $config);
        }

        if ($customerData['service_type'] === 'pppoe') {
            if (isset($customerData['username']) && ! empty($customerData['username'])) {
                $config['username'] = $customerData['username'];
            }
            if (isset($customerData['password']) && ! empty($customerData['password'])) {
                $config['password'] = $customerData['password'];
            }

            return $this->serviceFactory->createPppoeService($customer, $subscription, $config);
        }

        return ServiceResult::error('Invalid service type: '.$customerData['service_type']);
    }

    protected function createInitialInvoice(Customer $customer, Subscription $subscription): void
    {
        $invoiceNumber = 'INV-'.now()->format('Ymd').'-'.str_pad($customer->id, 4, '0', STR_PAD_LEFT);

        Invoice::create([
            'customer_id' => $customer->id,
            'subscription_id' => $subscription->id,
            'invoice_number' => $invoiceNumber,
            'amount' => $subscription->price,
            'tax_amount' => 0,
            'total_amount' => $subscription->price,
            'status' => 'pending',
            'issue_date' => now()->toDateString(),
            'due_date' => $subscription->next_billing_date,
            'billing_period_start' => $subscription->start_date,
            'billing_period_end' => now()->parse($subscription->next_billing_date)->subDay(),
        ]);
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Customer import job failed', [
            'batch_id' => $this->batchId,
            'customers_count' => count($this->customersData),
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
