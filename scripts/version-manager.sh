#!/bin/bash

# ISP Management System - Version Manager
# This script helps manage versions and releases

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[INFO]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_header() { echo -e "${BLUE}$1${NC}"; }

# Configuration
CHANGELOG_FILE="CHANGELOG.md"
VERSION_FILE="VERSION"
RELEASE_NOTES_DIR="docs/releases"

print_header "📋 ISP Management System - Version Manager"
print_header "=========================================="

# Create necessary directories
mkdir -p "$RELEASE_NOTES_DIR"

# Function to get current version
get_current_version() {
    if [[ -f "$VERSION_FILE" ]]; then
        grep "Version:" "$VERSION_FILE" | cut -d' ' -f2 || echo "0.0.0"
    else
        echo "0.0.0"
    fi
}

# Function to increment version
increment_version() {
    local version=$1
    local type=$2
    
    IFS='.' read -ra ADDR <<< "$version"
    local major=${ADDR[0]}
    local minor=${ADDR[1]}
    local patch=${ADDR[2]}
    
    case $type in
        "major")
            major=$((major + 1))
            minor=0
            patch=0
            ;;
        "minor")
            minor=$((minor + 1))
            patch=0
            ;;
        "patch")
            patch=$((patch + 1))
            ;;
    esac
    
    echo "$major.$minor.$patch"
}

# Function to create release
create_release() {
    local version=$1
    local type=$2
    local message="$3"
    
    print_status "Creating release $version..."
    
    # Update VERSION file
    cat > "$VERSION_FILE" << EOF
ISP Management System
Version: $version
Release Date: $(date)
Release Type: $type
Release Notes: See docs/releases/v$version.md
EOF
    
    # Create release notes
    local release_notes_file="$RELEASE_NOTES_DIR/v$version.md"
    cat > "$release_notes_file" << EOF
# Release Notes - Version $version

**Release Date:** $(date '+%Y-%m-%d')  
**Release Type:** $type

## Summary
$message

## What's New

### Features
- [Add new features here]

### Improvements
- [Add improvements here]

### Bug Fixes
- [Add bug fixes here]

### Database Changes
- [Add database migration details here]

## Upgrade Instructions

1. **Backup your system:**
   \`\`\`bash
   ./scripts/backup.sh
   \`\`\`

2. **Run the update:**
   \`\`\`bash
   ./update.sh
   \`\`\`

3. **Verify the installation:**
   - Check system functionality
   - Test MikroTik connections
   - Verify customizations

## Breaking Changes
- [List any breaking changes here]

## Migration Notes
- [Add any special migration instructions here]

## Support
If you encounter issues with this release:
1. Check the troubleshooting guide
2. Review the logs in \`storage/logs/\`
3. Contact support with your system details

---
**Previous Version:** [Add previous version here]  
**Next Version:** [Will be updated in next release]
EOF
    
    # Update CHANGELOG
    if [[ ! -f "$CHANGELOG_FILE" ]]; then
        cat > "$CHANGELOG_FILE" << EOF
# Changelog

All notable changes to the ISP Management System will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

EOF
    fi
    
    # Add new entry to changelog
    local temp_changelog=$(mktemp)
    cat > "$temp_changelog" << EOF
# Changelog

All notable changes to the ISP Management System will be documented in this file.

## [v$version] - $(date '+%Y-%m-%d')

### Summary
$message

### Added
- [New features]

### Changed
- [Changes in existing functionality]

### Fixed
- [Bug fixes]

### Removed
- [Removed features]

EOF
    
    # Append existing changelog (skip header)
    if [[ -f "$CHANGELOG_FILE" ]]; then
        tail -n +6 "$CHANGELOG_FILE" >> "$temp_changelog"
    fi
    
    mv "$temp_changelog" "$CHANGELOG_FILE"
    
    # Commit changes
    git add "$VERSION_FILE" "$release_notes_file" "$CHANGELOG_FILE"
    git commit -m "Release v$version: $message"
    
    # Create tag
    git tag -a "v$version" -m "Release v$version: $message"
    
    print_status "✅ Release v$version created successfully"
    print_status "📝 Release notes: $release_notes_file"
    print_status "🏷️  Git tag: v$version"
    
    echo ""
    print_warning "📋 Next Steps:"
    print_warning "1. Review and edit the release notes: $release_notes_file"
    print_warning "2. Update the changelog with specific changes"
    print_warning "3. Push the release: git push origin main --tags"
    print_warning "4. Notify ISP providers about the update"
}

# Function to list versions
list_versions() {
    print_status "📋 Available Versions:"
    echo ""
    
    # Show git tags
    if git tag -l "v*" | grep -q .; then
        print_status "Released Versions:"
        git tag -l "v*" | sort -V | tail -10
    else
        print_warning "No released versions found"
    fi
    
    echo ""
    
    # Show current version
    local current=$(get_current_version)
    print_status "Current Version: $current"
    
    # Show unreleased commits
    local last_tag=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
    if [[ -n "$last_tag" ]]; then
        local commits_since=$(git rev-list --count "$last_tag"..HEAD)
        if [[ $commits_since -gt 0 ]]; then
            print_warning "Unreleased commits: $commits_since"
        fi
    fi
}

# Function to show release info
show_release_info() {
    local version=$1
    
    if [[ -z "$version" ]]; then
        version=$(get_current_version)
    fi
    
    print_status "📋 Release Information - v$version"
    echo ""
    
    # Show version file info
    if [[ -f "$VERSION_FILE" ]]; then
        cat "$VERSION_FILE"
        echo ""
    fi
    
    # Show release notes if available
    local release_notes="$RELEASE_NOTES_DIR/v$version.md"
    if [[ -f "$release_notes" ]]; then
        print_status "📝 Release Notes:"
        cat "$release_notes"
    else
        print_warning "No release notes found for v$version"
    fi
}

# Main menu
case "${1:-menu}" in
    "create")
        current_version=$(get_current_version)
        print_status "Current version: $current_version"
        
        echo ""
        echo "Release Types:"
        echo "1. patch (bug fixes) - $current_version -> $(increment_version "$current_version" "patch")"
        echo "2. minor (new features) - $current_version -> $(increment_version "$current_version" "minor")"
        echo "3. major (breaking changes) - $current_version -> $(increment_version "$current_version" "major")"
        echo ""
        
        read -p "Select release type (1-3): " -n 1 -r
        echo
        
        case $REPLY in
            1) release_type="patch" ;;
            2) release_type="minor" ;;
            3) release_type="major" ;;
            *) print_error "Invalid selection"; exit 1 ;;
        esac
        
        new_version=$(increment_version "$current_version" "$release_type")
        
        echo ""
        read -p "Release message: " release_message
        
        if [[ -z "$release_message" ]]; then
            print_error "Release message is required"
            exit 1
        fi
        
        create_release "$new_version" "$release_type" "$release_message"
        ;;
        
    "list")
        list_versions
        ;;
        
    "info")
        show_release_info "$2"
        ;;
        
    "menu"|*)
        echo ""
        echo "Available commands:"
        echo "  create  - Create a new release"
        echo "  list    - List all versions"
        echo "  info    - Show release information"
        echo ""
        echo "Usage: $0 [command] [options]"
        echo ""
        echo "Examples:"
        echo "  $0 create                    # Create new release (interactive)"
        echo "  $0 list                      # List all versions"
        echo "  $0 info v1.2.0              # Show info for specific version"
        echo "  $0 info                      # Show info for current version"
        ;;
esac
