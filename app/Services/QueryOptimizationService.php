<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Models\Subscription;
use App\Services\OrganizationContext;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class QueryOptimizationService
{
    /**
     * Get optimized customer query with all necessary relationships
     */
    public static function optimizedCustomersQuery(): Builder
    {
        return Customer::query()
            ->with([
                'subscriptions' => function ($query) {
                    $query->where('status', 'active')
                        ->with(['bandwidthPlan:id,name,download_speed,upload_speed']);
                },
                'staticIpServices' => function ($query) {
                    $query->select('id', 'customer_id', 'status', 'ip_address', 'created_at')
                        ->with(['device:id,name', 'ipPool:id,name']);
                },
                'pppoeServices' => function ($query) {
                    $query->select('id', 'customer_id', 'status', 'username', 'created_at')
                        ->with(['device:id,name']);
                },
            ])
            ->select('id', 'name', 'email', 'phone', 'status', 'created_at');
    }

    /**
     * Get optimized static IP services query
     */
    public static function optimizedStaticIpServicesQuery(): Builder
    {
        return StaticIpService::query()
            ->with([
                'customer:id,name,email,status',
                'subscription:id,customer_id,name,price,status',
                'device:id,name,ip_address,status',
                'bandwidthPlan:id,name,download_speed,upload_speed',
                'ipPool:id,name,network_address,status',
            ]);
    }

    /**
     * Get optimized PPPoE services query
     */
    public static function optimizedPppoeServicesQuery(): Builder
    {
        return PppoeService::query()
            ->with([
                'customer:id,name,email,status',
                'subscription:id,customer_id,name,price,status',
                'device:id,name,ip_address,status',
                'bandwidthPlan:id,name,download_speed,upload_speed',
            ]);
    }

    /**
     * Get optimized subscriptions query
     */
    public static function optimizedSubscriptionsQuery(): Builder
    {
        return Subscription::query()
            ->with([
                'customer:id,name,email,phone,status',
                'bandwidthPlan:id,name,download_speed,upload_speed,price',
                'networkSite:id,name,location',
            ]);
    }

    /**
     * Get optimized invoices query
     */
    public static function optimizedInvoicesQuery(): Builder
    {
        return Invoice::query()
            ->with([
                'customer:id,name,email,phone',
                'subscription:id,name,price',
            ]);
    }

    /**
     * Get dashboard statistics with optimized queries (no caching)
     * FIXED: Now properly scoped to current organization.
     */
    public static function getDashboardStats(): array
    {
        // Get current organization ID from context
        $organizationId = app(OrganizationContext::class)->getId();

        if (!$organizationId) {
            throw new \Exception('No organization context available for dashboard stats');
        }

        // Single query for all customer stats - FIXED: Organization scoped with qualified column names
        $customerStats = DB::selectOne("
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN c.status = 'active' THEN 1 END) as active,
                COUNT(CASE WHEN c.status = 'suspended' THEN 1 END) as suspended
            FROM customers c
            WHERE c.organization_id = ?
        ", [$organizationId]);

        // Single query for all service stats - FIXED: Organization scoped
        $serviceStats = DB::selectOne("
            SELECT
                (SELECT COUNT(*) FROM static_ip_services s JOIN customers c ON s.customer_id = c.id WHERE c.organization_id = ?) as total_static_ip,
                (SELECT COUNT(*) FROM static_ip_services s JOIN customers c ON s.customer_id = c.id WHERE c.organization_id = ? AND s.status = 'active') as active_static_ip,
                (SELECT COUNT(*) FROM pppoe_services p JOIN customers c ON p.customer_id = c.id WHERE c.organization_id = ?) as total_pppoe,
                (SELECT COUNT(*) FROM pppoe_services p JOIN customers c ON p.customer_id = c.id WHERE c.organization_id = ? AND p.status = 'active') as active_pppoe
        ", [$organizationId, $organizationId, $organizationId, $organizationId]);

        // Single query for subscription stats - FIXED: Organization scoped with qualified column names
        $subscriptionStats = DB::selectOne("
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN s.status = 'active' THEN 1 END) as active,
                COUNT(CASE WHEN s.status = 'active' AND s.next_billing_date <= NOW() THEN 1 END) as billing_due
            FROM subscriptions s
            JOIN customers c ON s.customer_id = c.id
            WHERE c.organization_id = ?
        ", [$organizationId]);

        // Single query for invoice stats - FIXED: Organization scoped
        $invoiceStats = DB::selectOne("
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN i.status = 'pending' THEN 1 END) as pending,
                COUNT(CASE WHEN i.status = 'pending' AND i.due_date < NOW() THEN 1 END) as overdue,
                COALESCE(SUM(CASE WHEN i.status = 'pending' THEN i.total_amount END), 0) as total_amount
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            WHERE c.organization_id = ?
        ", [$organizationId]);

        // Single query for device stats - FIXED: Organization scoped with qualified column names
        $deviceStats = DB::selectOne("
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN d.status = 'active' THEN 1 END) as active,
                COUNT(CASE WHEN d.status = 'active' AND d.last_connected_at >= NOW() - INTERVAL '1 hour' THEN 1 END) as connected_recently
            FROM network_devices d
            WHERE d.organization_id = ?
        ", [$organizationId]);

        // Single query for IP pool stats - FIXED: Organization scoped with qualified column names
        $ipPoolStats = DB::selectOne("
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active
            FROM ip_pools p
            JOIN network_devices d ON p.device_id = d.id
            WHERE d.organization_id = ?
        ", [$organizationId]);

        return [
            'customers' => [
                'total' => $customerStats->total,
                'active' => $customerStats->active,
                'suspended' => $customerStats->suspended,
            ],
            'services' => [
                'total_static_ip' => $serviceStats->total_static_ip,
                'active_static_ip' => $serviceStats->active_static_ip,
                'total_pppoe' => $serviceStats->total_pppoe,
                'active_pppoe' => $serviceStats->active_pppoe,
            ],
            'subscriptions' => [
                'total' => $subscriptionStats->total,
                'active' => $subscriptionStats->active,
                'billing_due' => $subscriptionStats->billing_due,
            ],
            'invoices' => [
                'total' => $invoiceStats->total,
                'pending' => $invoiceStats->pending,
                'overdue' => $invoiceStats->overdue,
                'total_amount' => $invoiceStats->total_amount,
            ],
            'devices' => [
                'total' => $deviceStats->total,
                'active' => $deviceStats->active,
                'connected_recently' => $deviceStats->connected_recently,
            ],
            'ip_pools' => [
                'total' => $ipPoolStats->total,
                'active' => $ipPoolStats->active,
            ],
        ];
    }

    /**
     * Get recent activities with single query joins (no caching)
     * FIXED: Now properly scoped to current organization.
     */
    public static function getRecentActivities(int $limit = 10): array
    {
        // Get current organization ID from context
        $organizationId = app(OrganizationContext::class)->getId();

        if (!$organizationId) {
            throw new \Exception('No organization context available for recent activities');
        }

        // Single query with UNION for both service types - FIXED: Organization scoped
        $activities = DB::select("
            (
                SELECT
                    CONCAT('static_ip_', s.id) as id,
                    'static_ip_service' as type,
                    CONCAT('Static IP service for ', c.name, ' (', s.ip_address, ')') as description,
                    d.name as device,
                    s.status,
                    s.created_at
                FROM static_ip_services s
                JOIN customers c ON s.customer_id = c.id
                JOIN network_devices d ON s.device_id = d.id
                WHERE c.organization_id = ?
                ORDER BY s.created_at DESC
                LIMIT ?
            )
            UNION ALL
            (
                SELECT
                    CONCAT('pppoe_', p.id) as id,
                    'pppoe_service' as type,
                    CONCAT('PPPoE service for ', c.name, ' (', p.username, ')') as description,
                    d.name as device,
                    p.status,
                    p.created_at
                FROM pppoe_services p
                JOIN customers c ON p.customer_id = c.id
                JOIN network_devices d ON p.device_id = d.id
                WHERE c.organization_id = ?
                ORDER BY p.created_at DESC
                LIMIT ?
            )
            ORDER BY created_at DESC
            LIMIT ?
        ", [$organizationId, $limit, $organizationId, $limit, $limit]);

        return array_map(function ($activity) {
            return [
                'id' => $activity->id,
                'type' => $activity->type,
                'description' => $activity->description,
                'device' => $activity->device,
                'status' => $activity->status,
                'created_at' => $activity->created_at,
            ];
        }, $activities);
    }

    /**
     * Get device utilization stats with single query (no caching)
     * FIXED: Now properly scoped to current organization.
     */
    public static function getDeviceUtilizationStats(): array
    {
        // Get current organization ID from context
        $organizationId = app(OrganizationContext::class)->getId();

        if (!$organizationId) {
            throw new \Exception('No organization context available for device utilization stats');
        }

        $devices = DB::select("
            SELECT
                d.id,
                d.name,
                d.ip_address,
                d.detected_model,
                COALESCE(static_counts.total, 0) as static_ip_services_count,
                COALESCE(static_counts.active, 0) as active_static_ip_count,
                COALESCE(pppoe_counts.total, 0) as pppoe_services_count,
                COALESCE(pppoe_counts.active, 0) as active_pppoe_count
            FROM network_devices d
            LEFT JOIN (
                SELECT
                    s.device_id,
                    COUNT(*) as total,
                    COUNT(CASE WHEN s.status = 'active' THEN 1 END) as active
                FROM static_ip_services s
                JOIN customers c ON s.customer_id = c.id
                WHERE c.organization_id = ?
                GROUP BY s.device_id
            ) static_counts ON d.id = static_counts.device_id
            LEFT JOIN (
                SELECT
                    p.device_id,
                    COUNT(*) as total,
                    COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active
                FROM pppoe_services p
                JOIN customers c ON p.customer_id = c.id
                WHERE c.organization_id = ?
                GROUP BY p.device_id
            ) pppoe_counts ON d.id = pppoe_counts.device_id
            WHERE d.status = 'active' AND d.organization_id = ?
        ", [$organizationId, $organizationId, $organizationId]);

        return array_map(function ($device) {
            $totalServices = $device->static_ip_services_count + $device->pppoe_services_count;
            $activeServices = $device->active_static_ip_count + $device->active_pppoe_count;

            return [
                'device' => [
                    'id' => $device->id,
                    'name' => $device->name,
                    'ip_address' => $device->ip_address,
                    'type' => $device->detected_model,
                ],
                'services' => [
                    'total' => $totalServices,
                    'active' => $activeServices,
                    'utilization_percentage' => $totalServices > 0 ? round(($activeServices / $totalServices) * 100, 2) : 0,
                    'static_ip_count' => $device->static_ip_services_count,
                    'pppoe_count' => $device->pppoe_services_count,
                ],
            ];
        }, $devices);
    }

    /**
     * Get billing summary with optimized aggregation (no caching)
     * FIXED: Now properly scoped to current organization.
     */
    public static function getBillingSummary(): array
    {
        // Get current organization ID from context
        $organizationId = app(OrganizationContext::class)->getId();

        if (!$organizationId) {
            throw new \Exception('No organization context available for billing summary');
        }

        $now = now();
        $thisMonth = $now->format('Y-m');
        $lastMonth = $now->copy()->subMonth()->format('Y-m');

        // Use database-agnostic date functions
        $dbDriver = config('database.default');
        $connectionConfig = config("database.connections.{$dbDriver}");
        $isPostgreSQL = ($connectionConfig['driver'] ?? '') === 'pgsql';

        if ($isPostgreSQL) {
            $dateFormat = "TO_CHAR(i.created_at, 'YYYY-MM')";
        } else {
            $dateFormat = "DATE_FORMAT(i.created_at, '%Y-%m')";
        }

        $billingSummary = DB::selectOne("
            SELECT
                COUNT(CASE WHEN {$dateFormat} = ? THEN 1 END) as this_month_invoices,
                COALESCE(SUM(CASE WHEN {$dateFormat} = ? THEN i.total_amount END), 0) as this_month_total,
                COALESCE(SUM(CASE WHEN {$dateFormat} = ? AND i.status = 'paid' THEN i.total_amount END), 0) as this_month_paid,
                COUNT(CASE WHEN {$dateFormat} = ? THEN 1 END) as last_month_invoices,
                COALESCE(SUM(CASE WHEN {$dateFormat} = ? THEN i.total_amount END), 0) as last_month_total,
                COALESCE(SUM(CASE WHEN {$dateFormat} = ? AND i.status = 'paid' THEN i.total_amount END), 0) as last_month_paid,
                COUNT(CASE WHEN i.status = 'pending' AND i.due_date < NOW() THEN 1 END) as overdue_count,
                COALESCE(SUM(CASE WHEN i.status = 'pending' AND i.due_date < NOW() THEN i.total_amount END), 0) as overdue_amount
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            WHERE c.organization_id = ?
        ", [$thisMonth, $thisMonth, $thisMonth, $lastMonth, $lastMonth, $lastMonth, $organizationId]);

        return [
            'this_month' => [
                'invoices_generated' => $billingSummary->this_month_invoices,
                'total_amount' => $billingSummary->this_month_total,
                'paid_amount' => $billingSummary->this_month_paid,
            ],
            'last_month' => [
                'invoices_generated' => $billingSummary->last_month_invoices,
                'total_amount' => $billingSummary->last_month_total,
                'paid_amount' => $billingSummary->last_month_paid,
            ],
            'overdue' => [
                'count' => $billingSummary->overdue_count,
                'total_amount' => $billingSummary->overdue_amount,
            ],
        ];
    }

    /**
     * Clear all optimization caches (now just a placeholder)
     */
    public static function clearOptimizationCaches(): void
    {
        // No caching to clear - pure query optimization
    }

    /**
     * Get IP pool utilization with single optimized query (no caching)
     * FIXED: Now properly scoped to current organization.
     */
    public static function getIpPoolUtilization(): array
    {
        // Get current organization ID from context
        $organizationId = app(OrganizationContext::class)->getId();

        if (!$organizationId) {
            throw new \Exception('No organization context available for IP pool utilization');
        }

        $pools = DB::select("
            SELECT
                p.id,
                p.name,
                p.network_address,
                p.total_addresses,
                p.available_addresses,
                p.assigned_addresses,
                d.name as device_name
            FROM ip_pools p
            JOIN network_devices d ON p.device_id = d.id
            WHERE p.status = 'active' AND d.organization_id = ?
        ", [$organizationId]);

        return array_map(function ($pool) {
            $total = $pool->total_addresses ?? 0;
            $assigned = $pool->assigned_addresses ?? 0;
            $available = $pool->available_addresses ?? 0;

            return [
                'pool' => [
                    'id' => $pool->id,
                    'name' => $pool->name,
                    'network_address' => $pool->network_address,
                    'device_name' => $pool->device_name,
                ],
                'utilization' => [
                    'total_ips' => $total,
                    'assigned' => $assigned,
                    'available' => $available,
                    'utilization_percentage' => $total > 0 ? round(($assigned / $total) * 100, 2) : 0,
                ],
            ];
        }, $pools);
    }

    /**
     * Get optimized customer query with single JOIN query (no N+1)
     */
    public static function optimizedCustomersQueryAdvanced(): Builder
    {
        return Customer::query()
            ->select([
                'customers.id',
                'customers.name',
                'customers.email',
                'customers.phone',
                'customers.status',
                'customers.created_at',
                DB::raw('COUNT(DISTINCT subscriptions.id) as subscriptions_count'),
                DB::raw('COUNT(DISTINCT CASE WHEN subscriptions.status = "active" THEN subscriptions.id END) as active_subscriptions_count'),
                DB::raw('COUNT(DISTINCT static_ip_services.id) as static_ip_count'),
                DB::raw('COUNT(DISTINCT pppoe_services.id) as pppoe_count'),
                DB::raw('COUNT(DISTINCT CASE WHEN static_ip_services.status = "active" THEN static_ip_services.id END) as active_static_ip_count'),
                DB::raw('COUNT(DISTINCT CASE WHEN pppoe_services.status = "active" THEN pppoe_services.id END) as active_pppoe_count'),
            ])
            ->leftJoin('subscriptions', 'customers.id', '=', 'subscriptions.customer_id')
            ->leftJoin('static_ip_services', 'customers.id', '=', 'static_ip_services.customer_id')
            ->leftJoin('pppoe_services', 'customers.id', '=', 'pppoe_services.customer_id')
            ->groupBy([
                'customers.id',
                'customers.name',
                'customers.email',
                'customers.phone',
                'customers.status',
                'customers.created_at',
            ]);
    }

    /**
     * Get optimized static IP services with single query
     */
    public static function optimizedStaticIpServicesQueryAdvanced(): Builder
    {
        return StaticIpService::query()
            ->select([
                'static_ip_services.*',
                'customers.name as customer_name',
                'customers.email as customer_email',
                'customers.status as customer_status',
                'subscriptions.name as subscription_name',
                'subscriptions.price as subscription_price',
                'subscriptions.status as subscription_status',
                'network_devices.name as device_name',
                'network_devices.ip_address as device_ip',
                'network_devices.status as device_status',
                'bandwidth_plans.name as bandwidth_plan_name',
                'bandwidth_plans.download_speed',
                'bandwidth_plans.upload_speed',
                'ip_pools.name as ip_pool_name',
                'ip_pools.network_address as ip_pool_network',
                'ip_pools.status as ip_pool_status',
            ])
            ->join('customers', 'static_ip_services.customer_id', '=', 'customers.id')
            ->join('subscriptions', 'static_ip_services.subscription_id', '=', 'subscriptions.id')
            ->join('network_devices', 'static_ip_services.device_id', '=', 'network_devices.id')
            ->leftJoin('bandwidth_plans', 'static_ip_services.bandwidth_plan_id', '=', 'bandwidth_plans.id')
            ->leftJoin('ip_pools', 'static_ip_services.ip_pool_id', '=', 'ip_pools.id');
    }

    /**
     * Get optimized PPPoE services with single query
     */
    public static function optimizedPppoeServicesQueryAdvanced(): Builder
    {
        return PppoeService::query()
            ->select([
                'pppoe_services.*',
                'customers.name as customer_name',
                'customers.email as customer_email',
                'customers.status as customer_status',
                'subscriptions.name as subscription_name',
                'subscriptions.price as subscription_price',
                'subscriptions.status as subscription_status',
                'network_devices.name as device_name',
                'network_devices.ip_address as device_ip',
                'network_devices.status as device_status',
                'bandwidth_plans.name as bandwidth_plan_name',
                'bandwidth_plans.download_speed',
                'bandwidth_plans.upload_speed',
            ])
            ->join('customers', 'pppoe_services.customer_id', '=', 'customers.id')
            ->join('subscriptions', 'pppoe_services.subscription_id', '=', 'subscriptions.id')
            ->join('network_devices', 'pppoe_services.device_id', '=', 'network_devices.id')
            ->leftJoin('bandwidth_plans', 'pppoe_services.bandwidth_plan_id', '=', 'bandwidth_plans.id');
    }
}
