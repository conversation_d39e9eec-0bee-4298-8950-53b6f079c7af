<?php

namespace App\Models;

use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Traits\BelongsToOrganization;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    use HasFactory, BelongsToOrganization;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'status',
        'user_id',
        'mpesa_id',
        'organization_id',
    ];

    protected $appends = [
        'full_address',
    ];

    /**
     * Get the user associated with the customer.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscriptions for the customer.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the invoices for the customer.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the active subscriptions for the customer.
     */
    public function activeSubscriptions()
    {
        return $this->subscriptions()->where('status', 'active');
    }

    /**
     * Get the static IP services for the customer.
     */
    public function staticIpServices(): HasMany
    {
        return $this->hasMany(StaticIpService::class);
    }

    /**
     * Get the PPPoE services for the customer.
     */
    public function pppoeServices(): HasMany
    {
        return $this->hasMany(PppoeService::class);
    }

    /**
     * Get the full address of the customer.
     */
    public function getFullAddressAttribute()
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Scope a query to only include active customers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get service status summary for this customer.
     */
    public function getServiceStatusSummaryAttribute()
    {
        $staticIpServices = $this->staticIpServices;
        $pppoeServices = $this->pppoeServices;

        $totalServices = $staticIpServices->count() + $pppoeServices->count();
        $suspendedServices = $staticIpServices->where('status', 'suspended')->count() +
                           $pppoeServices->where('status', 'suspended')->count();
        $activeServices = $totalServices - $suspendedServices;

        return [
            'total' => $totalServices,
            'active' => $activeServices,
            'suspended' => $suspendedServices,
            'has_suspended' => $suspendedServices > 0,
            'all_suspended' => $totalServices > 0 && $suspendedServices === $totalServices,
            'suspended_service_names' => $this->getSuspendedServiceNames(),
        ];
    }

    /**
     * Get names of suspended services.
     */
    public function getSuspendedServiceNames()
    {
        $suspendedServices = [];

        foreach ($this->staticIpServices->where('status', 'suspended') as $service) {
            $suspendedServices[] = "Static IP ({$service->ip_address})";
        }

        foreach ($this->pppoeServices->where('status', 'suspended') as $service) {
            $suspendedServices[] = "PPPoE ({$service->username})";
        }

        return $suspendedServices;
    }

    /**
     * Check if customer has any suspended services.
     */
    public function hasSuspendedServices(): bool
    {
        return $this->staticIpServices->where('status', 'suspended')->count() > 0 ||
               $this->pppoeServices->where('status', 'suspended')->count() > 0;
    }

    /**
     * Get customer by M-Pesa ID or invoice number.
     */
    public static function findByMpesaIdOrInvoice(string $identifier): ?Customer
    {
        // First try to find by M-Pesa ID
        $customer = static::where('mpesa_id', $identifier)->first();

        if ($customer) {
            return $customer;
        }

        // If not found, try to find by invoice number
        $invoice = \App\Models\Invoice::where('invoice_number', $identifier)->first();

        return $invoice ? $invoice->customer : null;
    }

    /**
     * Generate M-Pesa ID for this customer.
     */
    public function generateMpesaId(?string $manualId = null): string
    {
        $service = app(\App\Services\MpesaIdService::class);

        return $service->generateMpesaId($this, $manualId);
    }

    /**
     * Check if customer has M-Pesa ID.
     */
    public function hasMpesaId(): bool
    {
        return ! empty($this->mpesa_id);
    }
}
