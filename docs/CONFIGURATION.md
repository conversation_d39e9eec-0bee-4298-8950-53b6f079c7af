# ⚙️ ISP Management System - Configuration Guide

This guide covers detailed configuration options for your ISP Management System.

## 🏢 **Company Configuration**

### **1. Basic Company Settings**
Navigate to: **Admin Panel → Settings → Company**

```
Company Name: Your ISP Company Name
Business Registration: Your business registration number
Tax ID: Your tax identification number
Address: Complete business address
Phone: Primary contact number
Email: Primary business email
Website: Your company website
```

### **2. Branding Configuration**
```
Logo: Upload your company logo (recommended: 200x60px PNG)
Favicon: Upload favicon (32x32px ICO)
Primary Color: Your brand primary color (#hex)
Secondary Color: Your brand secondary color (#hex)
```

### **3. Timezone and Locale**
```
Timezone: Africa/Nairobi (or your timezone)
Currency: KES (or your currency)
Date Format: DD/MM/YYYY (or preferred format)
Language: English (or your language)
```

## 💰 **Billing Configuration**

### **1. Invoice Settings**
Navigate to: **Admin Panel → Settings → Billing**

```
Invoice Prefix: INV-
Invoice Number Format: Sequential/Date-based
Due Days: 30 (days after invoice date)
Late Fee: 5% (percentage or fixed amount)
Grace Period: 7 days
Auto-suspend: Yes/No
```

### **2. Payment Methods**
```
M-Pesa: Enable/Disable
Bank Transfer: Enable/Disable
Cash: Enable/Disable
Credit Card: Enable/Disable (requires payment gateway)
```

### **3. M-Pesa Configuration**
```env
# Add to .env file
MPESA_CONSUMER_KEY=your_consumer_key
MPESA_CONSUMER_SECRET=your_consumer_secret
MPESA_SHORTCODE=your_shortcode
MPESA_PASSKEY=your_passkey
MPESA_ENVIRONMENT=sandbox  # or production
```

## 📧 **Email Configuration**

### **1. SMTP Settings**
Edit `.env` file:
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your ISP Name"
```

### **2. Email Templates**
Navigate to: **Admin Panel → Settings → Email Templates**

Customize templates for:
- Welcome emails
- Invoice notifications
- Payment confirmations
- Service suspension notices
- Password reset emails

### **3. Test Email Configuration**
```bash
# Test email sending
docker-compose exec app php artisan tinker
# In tinker:
Mail::raw('Test email', function($message) {
    $message->to('<EMAIL>')->subject('Test Email');
});
```

## 🌐 **Network Configuration**

### **1. Network Sites**
Navigate to: **Admin Panel → Network → Sites**

Create network sites for different locations:
```
Site Name: Main Office
Location: Nairobi, Kenya
Address: Complete address
Coordinates: Latitude, Longitude (optional)
Contact Person: Site manager name
Phone: Site contact number
```

### **2. Network Devices**
Navigate to: **Admin Panel → Network → Devices**

Add your MikroTik routers:
```
Device Name: Router-Main-01
IP Address: ***********
Username: admin
Password: your-router-password
API Port: 8728 (default)
Site: Select network site
Status: Active
```

### **3. IP Pool Configuration**
Navigate to: **Admin Panel → Network → IP Pools**

Create IP pools for customer assignments:
```
Pool Name: Residential-Pool-1
Network: *************/24
Gateway: *************
DNS Primary: *******
DNS Secondary: *******
Device: Select MikroTik router
Interface: ether2
```

## 📊 **Bandwidth Plans**

### **1. Create Bandwidth Plans**
Navigate to: **Admin Panel → Services → Bandwidth Plans**

Example plans:
```
Plan Name: Home Basic
Download Speed: 10 Mbps
Upload Speed: 2 Mbps
Price: 2000 KES
Billing Cycle: Monthly
Data Limit: Unlimited
Burst Limit: 15 Mbps
```

### **2. Plan Categories**
```
Residential Plans:
- Home Basic (10/2 Mbps)
- Home Standard (25/5 Mbps)
- Home Premium (50/10 Mbps)

Business Plans:
- Business Starter (20/5 Mbps)
- Business Pro (50/10 Mbps)
- Business Enterprise (100/20 Mbps)
```

## 👥 **User Management**

### **1. User Roles**
Navigate to: **Admin Panel → Users → Roles**

Default roles:
```
Super Admin: Full system access
Admin: Most administrative functions
Manager: Customer and billing management
Technician: Network and technical support
Support: Customer support only
```

### **2. Create Staff Users**
Navigate to: **Admin Panel → Users → Staff**

```
Name: Staff member name
Email: <EMAIL>
Role: Select appropriate role
Department: Technical/Sales/Support
Phone: Contact number
Status: Active
```

### **3. Customer Portal Settings**
Navigate to: **Admin Panel → Settings → Customer Portal**

```
Allow Self Registration: Yes/No
Require Email Verification: Yes
Allow Password Reset: Yes
Show Usage Statistics: Yes
Allow Plan Changes: Yes/No
Allow Service Requests: Yes
```

## 🔧 **System Settings**

### **1. Application Settings**
Edit `.env` file:
```env
APP_NAME="Your ISP Management System"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Session settings
SESSION_LIFETIME=120
SESSION_ENCRYPT=true

# Cache settings
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
```

### **2. Database Optimization**
```bash
# Optimize database
docker-compose exec app php artisan optimize

# Clear caches
docker-compose exec app php artisan cache:clear
docker-compose exec app php artisan config:clear
docker-compose exec app php artisan route:clear
```

### **3. Backup Configuration**
```bash
# Set up automated backups
crontab -e

# Add backup job (daily at 2 AM)
0 2 * * * cd /path/to/isp-management && ./scripts/backup.sh
```

## 📱 **API Configuration**

### **1. Enable API Access**
Navigate to: **Admin Panel → Settings → API**

```
API Status: Enabled
Rate Limiting: 60 requests per minute
Authentication: Token-based
Documentation: /api/documentation
```

### **2. Generate API Keys**
Navigate to: **Admin Panel → API → Keys**

```
Key Name: Mobile App
Permissions: Read customers, Create tickets
Expires: Never/Date
Status: Active
```

## 🔒 **Security Configuration**

### **1. Two-Factor Authentication**
Navigate to: **Admin Panel → Settings → Security**

```
2FA Required: For admin users
2FA Method: Google Authenticator
Backup Codes: Generate for users
Session Timeout: 30 minutes
```

### **2. Password Policy**
```
Minimum Length: 8 characters
Require Uppercase: Yes
Require Numbers: Yes
Require Symbols: Yes
Password History: Remember last 5
Expiry: 90 days (optional)
```

### **3. Login Security**
```
Max Login Attempts: 5
Lockout Duration: 15 minutes
IP Whitelist: Optional admin IPs
Login Notifications: Email on new device
```

## 📊 **Monitoring Configuration**

### **1. System Monitoring**
```bash
# Check system health
./check-updates.sh

# Monitor logs
docker-compose logs -f app

# Check resource usage
docker stats
```

### **2. Alert Configuration**
Navigate to: **Admin Panel → Settings → Alerts**

```
Low Balance Alert: 500 KES
Service Down Alert: Immediate
Payment Overdue: 7 days
High Usage Alert: 90% of limit
```

## 🔄 **Update Configuration**

### **1. Automatic Updates**
```bash
# Check update settings
./check-updates.sh --setup

# Configure update schedule
crontab -e
# 0 2 * * * cd /path/to/system && ./check-updates.sh --auto
```

### **2. Backup Before Updates**
```bash
# Ensure backups are working
./scripts/backup.sh

# Test restore process
./scripts/restore.sh backup-file.sql
```

## 📋 **Configuration Checklist**

### **Initial Setup**
- [ ] Company information configured
- [ ] Branding applied (logo, colors)
- [ ] Email settings tested
- [ ] Payment methods configured
- [ ] Network sites created
- [ ] MikroTik devices added
- [ ] IP pools configured
- [ ] Bandwidth plans created

### **Security Setup**
- [ ] Admin password changed
- [ ] SSL certificate installed
- [ ] Firewall configured
- [ ] 2FA enabled for admins
- [ ] Password policy set
- [ ] Login security configured

### **Operational Setup**
- [ ] Staff users created
- [ ] Customer portal configured
- [ ] Billing settings configured
- [ ] Email templates customized
- [ ] Backup system tested
- [ ] Monitoring alerts set
- [ ] API access configured (if needed)

## 🆘 **Configuration Troubleshooting**

### **Email Not Working**
```bash
# Test SMTP connection
docker-compose exec app php artisan tinker
# Test with: Mail::raw('test', fn($m) => $m->to('<EMAIL>'));
```

### **MikroTik Connection Failed**
```bash
# Test API connection
telnet router-ip 8728

# Check credentials in device settings
# Verify API service is enabled on MikroTik
```

### **Payment Gateway Issues**
```bash
# Check M-Pesa credentials
# Verify callback URLs are accessible
# Check logs: docker-compose logs app | grep mpesa
```

---

**Configuration Complete!** ⚙️

Your ISP Management System is now fully configured and ready for operation. Proceed to the [User Guide](USER-GUIDE.md) to learn how to use the system effectively.
