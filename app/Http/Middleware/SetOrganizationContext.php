<?php

namespace App\Http\Middleware;

use App\Services\OrganizationContext;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SetOrganizationContext
{
    protected OrganizationContext $organizationContext;

    public function __construct(OrganizationContext $organizationContext)
    {
        $this->organizationContext = $organizationContext;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only set organization context for authenticated users
        if (Auth::check()) {
            $user = Auth::user();
            
            // Check if user has an organization
            if ($user->organization_id) {
                $this->organizationContext->setOrganizationById($user->organization_id);
                
                // Verify organization is active
                if (!$this->organizationContext->isActive()) {
                    Auth::logout();
                    return redirect()->route('login')->with('error', 'Your organization is not active.');
                }
            } else {
                // User doesn't belong to any organization
                Auth::logout();
                return redirect()->route('login')->with('error', 'You must be assigned to an organization to access this system.');
            }
        }

        return $next($request);
    }
}
