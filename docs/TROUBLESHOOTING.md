# 🛠️ ISP Management System - Troubleshooting Guide

This guide helps you diagnose and resolve common issues with the ISP Management System.

## 🚨 **Emergency Procedures**

### **System Down - Quick Recovery**
```bash
# 1. Check system status
docker-compose ps

# 2. Restart all services
docker-compose restart

# 3. If still down, rebuild and restart
docker-compose down
docker-compose up -d --build

# 4. Check logs for errors
docker-compose logs app
```

### **Database Connection Lost**
```bash
# 1. Check database container
docker-compose ps postgres

# 2. Restart database
docker-compose restart postgres

# 3. Wait 30 seconds, then restart app
sleep 30
docker-compose restart app

# 4. Verify connection
docker-compose exec app php artisan migrate:status
```

### **Customer Services Not Working**
```bash
# 1. Check MikroTik connectivity
docker-compose exec app php artisan mikrotik:test-connection

# 2. Restart queue workers
docker-compose restart queue

# 3. Check for failed jobs
docker-compose exec app php artisan queue:failed
```

## 🔍 **Diagnostic Commands**

### **System Health Check**
```bash
# Overall system status
docker-compose ps

# Check disk space
df -h

# Check memory usage
free -h

# Check CPU usage
top

# Check network connectivity
ping google.com
```

### **Application Diagnostics**
```bash
# Check application logs
docker-compose logs app --tail=50

# Check queue status
docker-compose exec app php artisan queue:work --once

# Test database connection
docker-compose exec app php artisan tinker
# In tinker: DB::connection()->getPdo();

# Check cache status
docker-compose exec app php artisan cache:clear
```

### **Network Diagnostics**
```bash
# Test MikroTik API connection
telnet router-ip 8728

# Check port accessibility
nmap -p 8728 router-ip

# Test DNS resolution
nslookup your-domain.com

# Check SSL certificate
openssl s_client -connect your-domain.com:443
```

## 🐛 **Common Issues and Solutions**

### **Installation Issues**

#### **Docker Permission Denied**
**Problem**: `permission denied while trying to connect to the Docker daemon socket`

**Solution**:
```bash
# Add user to docker group
sudo usermod -aG docker $USER

# Logout and login again, or run:
newgrp docker

# Verify access
docker ps
```

#### **Port Already in Use**
**Problem**: `Port 80 is already in use`

**Solution**:
```bash
# Find what's using the port
sudo netstat -tulpn | grep :80

# Stop conflicting service
sudo systemctl stop apache2  # or nginx

# Or change port in docker-compose.yml
# ports: "8080:80"
```

#### **Insufficient Disk Space**
**Problem**: `No space left on device`

**Solution**:
```bash
# Check disk usage
df -h

# Clean Docker images
docker system prune -a

# Clean logs
sudo journalctl --vacuum-time=7d

# Remove old backups
find ./backups -type f -mtime +30 -delete
```

### **Application Issues**

#### **500 Internal Server Error**
**Problem**: White screen or 500 error

**Solution**:
```bash
# Check application logs
docker-compose logs app

# Clear all caches
docker-compose exec app php artisan optimize:clear

# Check file permissions
docker-compose exec app chown -R www-data:www-data storage bootstrap/cache

# Restart application
docker-compose restart app
```

#### **Database Migration Failed**
**Problem**: Migration errors during update

**Solution**:
```bash
# Check migration status
docker-compose exec app php artisan migrate:status

# Rollback last migration
docker-compose exec app php artisan migrate:rollback

# Try migration again
docker-compose exec app php artisan migrate

# If still failing, check database logs
docker-compose logs postgres
```

#### **Queue Jobs Not Processing**
**Problem**: Background jobs stuck or not running

**Solution**:
```bash
# Check queue worker status
docker-compose ps queue

# Restart queue worker
docker-compose restart queue

# Check failed jobs
docker-compose exec app php artisan queue:failed

# Retry failed jobs
docker-compose exec app php artisan queue:retry all

# Clear all jobs (if needed)
docker-compose exec app php artisan queue:flush
```

### **Network and Connectivity Issues**

#### **MikroTik Connection Failed**
**Problem**: Cannot connect to MikroTik router

**Solution**:
```bash
# Test basic connectivity
ping router-ip

# Test API port
telnet router-ip 8728

# Check MikroTik API service
# On MikroTik: /ip service print

# Verify credentials
# On MikroTik: /user print

# Check firewall rules
# On MikroTik: /ip firewall filter print
```

#### **Customer Cannot Connect**
**Problem**: Customer reports no internet access

**Diagnosis**:
```bash
# 1. Check customer status in system
# Admin Panel → Customers → Search customer

# 2. Check service status
docker-compose exec app php artisan customer:status customer-id

# 3. Check MikroTik configuration
# On MikroTik: /ppp secret print where name=customer-username
# On MikroTik: /ppp active print where name=customer-username

# 4. Check bandwidth allocation
# On MikroTik: /queue simple print where name=customer-username
```

**Solutions**:
```bash
# Reactivate customer service
docker-compose exec app php artisan customer:activate customer-id

# Reset customer password
# Admin Panel → Customers → Edit → Reset Password

# Check payment status
# Admin Panel → Billing → Customer Account

# Restart customer session
# On MikroTik: /ppp active remove customer-session-id
```

#### **Slow Performance**
**Problem**: System or customer connections are slow

**Diagnosis**:
```bash
# Check system resources
docker stats

# Check database performance
docker-compose exec app php artisan telescope:clear

# Check network utilization
# On MikroTik: /interface monitor-traffic ether1

# Check bandwidth allocation
# On MikroTik: /queue simple print stats
```

**Solutions**:
```bash
# Optimize database
docker-compose exec app php artisan optimize

# Clear caches
docker-compose exec app php artisan cache:clear

# Restart services
docker-compose restart

# Check for bandwidth over-allocation
# Review total allocated vs available bandwidth
```

### **Billing and Payment Issues**

#### **M-Pesa Payments Not Reflecting**
**Problem**: Customer paid via M-Pesa but payment not recorded

**Diagnosis**:
```bash
# Check M-Pesa logs
docker-compose logs app | grep -i mpesa

# Check webhook logs
tail -f storage/logs/webhooks.log

# Verify M-Pesa configuration
# Check .env file for correct credentials
```

**Solutions**:
```bash
# Manually record payment
# Admin Panel → Billing → Add Payment

# Resync M-Pesa transactions
docker-compose exec app php artisan mpesa:sync

# Check M-Pesa callback URL accessibility
curl -X POST https://your-domain.com/api/mpesa/callback
```

#### **Invoices Not Generating**
**Problem**: Automatic invoices not created

**Solution**:
```bash
# Check scheduler status
docker-compose ps scheduler

# Run billing manually
docker-compose exec app php artisan billing:generate

# Check scheduled tasks
docker-compose exec app php artisan schedule:list

# Restart scheduler
docker-compose restart scheduler
```

### **Email Issues**

#### **Emails Not Sending**
**Problem**: System emails not being delivered

**Diagnosis**:
```bash
# Test email configuration
docker-compose exec app php artisan tinker
# In tinker: Mail::raw('Test', function($m) { $m->to('<EMAIL>'); });

# Check mail logs
docker-compose logs app | grep -i mail

# Verify SMTP settings in .env
```

**Solutions**:
```bash
# Update email configuration
nano .env
# Update MAIL_* settings

# Restart application
docker-compose restart app

# Test with different SMTP provider
# Try Gmail, SendGrid, or Mailgun
```

## 📊 **Performance Monitoring**

### **System Metrics**
```bash
# Monitor resource usage
docker stats --no-stream

# Check disk I/O
iostat -x 1

# Monitor network traffic
iftop

# Check system load
uptime
```

### **Application Metrics**
```bash
# Check response times
curl -w "@curl-format.txt" -o /dev/null -s https://your-domain.com

# Monitor database queries
docker-compose exec app php artisan telescope:clear

# Check cache hit rates
docker-compose exec app php artisan cache:table
```

### **Database Performance**
```bash
# Check slow queries
docker-compose exec postgres psql -U isp_user -d isp_management -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;"

# Check database size
docker-compose exec postgres psql -U isp_user -d isp_management -c "
SELECT pg_size_pretty(pg_database_size('isp_management'));"
```

## 🔧 **Maintenance Tasks**

### **Daily Maintenance**
```bash
# Check system health
./check-updates.sh

# Review error logs
docker-compose logs app --since=24h | grep -i error

# Check disk space
df -h

# Verify backups
ls -la backups/
```

### **Weekly Maintenance**
```bash
# Clean old logs
docker-compose exec app php artisan log:clear

# Optimize database
docker-compose exec app php artisan optimize

# Update system packages
sudo apt update && sudo apt upgrade

# Check SSL certificate expiry
openssl x509 -in /etc/ssl/certs/your-cert.pem -noout -dates
```

### **Monthly Maintenance**
```bash
# Full system backup
./scripts/backup.sh

# Database optimization
docker-compose exec postgres vacuumdb -U isp_user -d isp_management --analyze

# Security updates
./update.sh

# Performance review
# Review system metrics and optimize as needed
```

## 📞 **Getting Help**

### **Log Collection**
When contacting support, collect these logs:
```bash
# System logs
docker-compose logs > system-logs.txt

# Application logs
cp storage/logs/laravel.log app-logs.txt

# System information
docker-compose ps > system-status.txt
df -h >> system-status.txt
free -h >> system-status.txt
```

### **Support Information**
Include this information when requesting support:
- System version (`cat VERSION`)
- Error messages (exact text)
- Steps to reproduce the issue
- System logs (as collected above)
- Recent changes or updates

### **Emergency Contacts**
- **Technical Support**: <EMAIL>
- **Emergency Phone**: +1-xxx-xxx-xxxx
- **Documentation**: https://docs.your-provider.com
- **Community Forum**: https://forum.your-provider.com

## 📋 **Troubleshooting Checklist**

### **Before Contacting Support**
- [ ] Checked system logs
- [ ] Restarted affected services
- [ ] Verified network connectivity
- [ ] Checked disk space and resources
- [ ] Reviewed recent changes
- [ ] Attempted basic solutions
- [ ] Collected relevant logs
- [ ] Documented error messages

### **System Recovery Checklist**
- [ ] System backup available
- [ ] Database backup verified
- [ ] Recovery procedure tested
- [ ] Rollback plan prepared
- [ ] Stakeholders notified
- [ ] Maintenance window scheduled

---

**Troubleshooting Guide Complete!** 🛠️

This guide covers the most common issues and their solutions. For issues not covered here, contact support with the collected logs and system information.
