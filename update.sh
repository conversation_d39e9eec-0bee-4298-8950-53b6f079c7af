#!/bin/bash

# ISP Management System - Auto Updater
# This script safely updates the system while preserving data and customizations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[INFO]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_header() { echo -e "${BLUE}$1${NC}"; }

# Configuration
BACKUP_DIR="./backups/updates"
CUSTOM_DIR="./custom"
UPDATE_LOG="./storage/logs/updates.log"
VERSION_FILE="./VERSION"
ROLLBACK_POINT=""

# Create necessary directories
mkdir -p "$BACKUP_DIR" "$CUSTOM_DIR" "$(dirname "$UPDATE_LOG")"

# Logging function
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$UPDATE_LOG"
    echo "$1"
}

print_header "🚀 ISP Management System Updater"
print_header "================================="

# Check if we're in the right directory
if [[ ! -f "artisan" ]] || [[ ! -f "composer.json" ]]; then
    print_error "This doesn't appear to be an ISP Management System directory"
    print_error "Please run this script from the root of your installation"
    exit 1
fi

# Get current version
CURRENT_VERSION="unknown"
if [[ -f "$VERSION_FILE" ]]; then
    CURRENT_VERSION=$(grep "Version:" "$VERSION_FILE" | cut -d' ' -f2 || echo "unknown")
fi

print_status "Current version: $CURRENT_VERSION"

# Check for updates
print_status "Checking for updates..."
git fetch origin --tags

# Get latest version from remote
LATEST_VERSION=$(git describe --tags --abbrev=0 origin/main 2>/dev/null || echo "")
if [[ -z "$LATEST_VERSION" ]]; then
    print_warning "No version tags found. Checking for commits..."
    LATEST_COMMIT=$(git rev-parse origin/main)
    CURRENT_COMMIT=$(git rev-parse HEAD)
    
    if [[ "$LATEST_COMMIT" == "$CURRENT_COMMIT" ]]; then
        print_status "✅ You're already on the latest version"
        exit 0
    fi
    LATEST_VERSION="latest-commit"
fi

print_status "Latest version available: $LATEST_VERSION"

# Check if update is needed
if [[ "$CURRENT_VERSION" == "$LATEST_VERSION" ]]; then
    print_status "✅ You're already on the latest version"
    exit 0
fi

# Confirm update
echo ""
print_warning "⚠️  This will update your system from $CURRENT_VERSION to $LATEST_VERSION"
print_warning "⚠️  A backup will be created automatically"
echo ""
read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Update cancelled"
    exit 0
fi

# Create rollback point
ROLLBACK_POINT="rollback-$(date +%Y%m%d-%H%M%S)"
print_status "Creating rollback point: $ROLLBACK_POINT"
git tag "$ROLLBACK_POINT" HEAD

# Pre-update backup
print_status "Creating backup..."
BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
mkdir -p "$BACKUP_PATH"

# Backup critical files and directories
cp -r .env "$BACKUP_PATH/" 2>/dev/null || true
cp -r storage/ "$BACKUP_PATH/" 2>/dev/null || true
cp -r "$CUSTOM_DIR/" "$BACKUP_PATH/" 2>/dev/null || true
cp -r public/storage "$BACKUP_PATH/" 2>/dev/null || true

# Backup database
print_status "Backing up database..."
if command -v docker-compose &> /dev/null && docker-compose ps postgres | grep -q "Up"; then
    docker-compose exec -T postgres pg_dump -U isp_user isp_management > "$BACKUP_PATH/database.sql"
    print_status "✅ Database backup created"
else
    print_warning "Could not create database backup - Docker not running or postgres not found"
fi

log_message "Backup created: $BACKUP_PATH"

# Put application in maintenance mode
print_status "Enabling maintenance mode..."
php artisan down --message="System update in progress" --retry=60 || true

# Function to restore from backup
restore_backup() {
    print_error "Restoring from backup..."
    
    # Restore files
    cp -r "$BACKUP_PATH/.env" ./ 2>/dev/null || true
    cp -r "$BACKUP_PATH/storage/"* storage/ 2>/dev/null || true
    cp -r "$BACKUP_PATH/custom/"* "$CUSTOM_DIR/" 2>/dev/null || true
    
    # Restore database if backup exists
    if [[ -f "$BACKUP_PATH/database.sql" ]]; then
        print_status "Restoring database..."
        if command -v docker-compose &> /dev/null; then
            docker-compose exec -T postgres psql -U isp_user -d isp_management < "$BACKUP_PATH/database.sql"
        fi
    fi
    
    # Rollback git changes
    if [[ -n "$ROLLBACK_POINT" ]]; then
        git reset --hard "$ROLLBACK_POINT"
    fi
    
    php artisan up || true
    print_error "System restored to previous state"
}

# Set trap for cleanup on error
trap 'restore_backup; exit 1' ERR

# Stash any local changes
print_status "Stashing local changes..."
git stash push -m "Auto-stash before update $(date)" || true

# Perform the update
print_status "Downloading updates..."
if [[ "$LATEST_VERSION" == "latest-commit" ]]; then
    git pull origin main
else
    git checkout "$LATEST_VERSION"
fi

# Update dependencies
print_status "Updating dependencies..."
if command -v docker-compose &> /dev/null; then
    docker-compose run --rm app composer install --no-dev --optimize-autoloader
else
    composer install --no-dev --optimize-autoloader
fi

# Run database migrations
print_status "Running database migrations..."
if command -v docker-compose &> /dev/null; then
    docker-compose run --rm app php artisan migrate --force
else
    php artisan migrate --force
fi

# Clear caches
print_status "Clearing caches..."
if command -v docker-compose &> /dev/null; then
    docker-compose run --rm app php artisan config:clear
    docker-compose run --rm app php artisan cache:clear
    docker-compose run --rm app php artisan route:clear
    docker-compose run --rm app php artisan view:clear
else
    php artisan config:clear
    php artisan cache:clear
    php artisan route:clear
    php artisan view:clear
fi

# Run post-update scripts if they exist
if [[ -f "scripts/post-update.sh" ]]; then
    print_status "Running post-update scripts..."
    chmod +x scripts/post-update.sh
    ./scripts/post-update.sh
fi

# Update version file
cat > "$VERSION_FILE" << EOF
ISP Management System
Version: $LATEST_VERSION
Updated: $(date)
Previous Version: $CURRENT_VERSION
Rollback Point: $ROLLBACK_POINT
EOF

# Restore custom files if they exist
if [[ -d "$BACKUP_PATH/custom" ]]; then
    print_status "Restoring custom files..."
    cp -r "$BACKUP_PATH/custom/"* "$CUSTOM_DIR/" 2>/dev/null || true
fi

# Restart services
print_status "Restarting services..."
if command -v docker-compose &> /dev/null; then
    docker-compose restart app queue scheduler
fi

# Disable maintenance mode
print_status "Disabling maintenance mode..."
php artisan up

# Clean up old rollback points (keep last 5)
print_status "Cleaning up old rollback points..."
git tag -l "rollback-*" | sort -r | tail -n +6 | xargs -r git tag -d

log_message "Update completed successfully: $CURRENT_VERSION -> $LATEST_VERSION"

echo ""
print_status "🎉 Update completed successfully!"
print_status "✅ Version: $CURRENT_VERSION → $LATEST_VERSION"
print_status "📁 Backup: $BACKUP_PATH"
print_status "🔄 Rollback point: $ROLLBACK_POINT"
echo ""
print_status "📋 Post-Update Checklist:"
print_status "  1. Test your system functionality"
print_status "  2. Check that customizations are intact"
print_status "  3. Verify database integrity"
print_status "  4. Test MikroTik connections"
echo ""
print_warning "💡 If you encounter issues, run: ./rollback.sh $ROLLBACK_POINT"
echo ""

# Remove trap
trap - ERR
