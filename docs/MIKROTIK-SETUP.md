# 🌐 MikroTik Integration Guide

This guide explains how to configure your MikroTik routers to work with the ISP Management System.

## 📋 **Prerequisites**

### **MikroTik Requirements**
- RouterOS 6.40+ (recommended 7.x)
- API service enabled
- Sufficient user permissions
- Network connectivity to management system

### **Supported Features**
- ✅ Customer service provisioning
- ✅ Bandwidth management
- ✅ PPPoE server configuration
- ✅ Static IP assignment
- ✅ Real-time monitoring
- ✅ Automatic service suspension/activation

## 🔧 **MikroTik Router Configuration**

### **Step 1: Enable API Service**

Connect to your MikroTik router via Winbox or SSH:

```bash
# Enable API service
/ip service enable api

# Set API port (default 8728)
/ip service set api port=8728

# Enable API-SSL for secure connection (optional)
/ip service enable api-ssl
/ip service set api-ssl port=8729
```

### **Step 2: Create Management User**

Create a dedicated user for the management system:

```bash
# Create user group with necessary permissions
/user group add name=isp-management policy=api,read,write,policy,test,password,sniff,sensitive,romon

# Create user
/user add name=isp-manager password=SecurePassword123 group=isp-management

# Verify user creation
/user print
```

### **Step 3: Configure PPPoE Server**

For PPPoE-based services:

```bash
# Create PPPoE server profile
/ppp profile add name=default-profile local-address=*********** remote-address=*************-*************54 dns-server=*******,*******

# Enable PPPoE server
/interface pppoe-server server add service-name=ISP-PPPoE interface=ether2 default-profile=default-profile authentication=pap,chap,mschap1,mschap2

# Create bandwidth profiles
/queue type add name=pcq-download kind=pcq pcq-rate=0 pcq-limit=50 pcq-classifier=dst-address pcq-total-limit=2000
/queue type add name=pcq-upload kind=pcq pcq-rate=0 pcq-limit=50 pcq-classifier=src-address pcq-total-limit=2000
```

### **Step 4: Configure DHCP Server (for Static IP)**

For static IP services:

```bash
# Create DHCP server
/ip pool add name=static-pool ranges=**************-***************

/ip dhcp-server network add address=*************/24 gateway=************* dns-server=*******,*******

/ip dhcp-server add name=static-dhcp interface=ether3 address-pool=static-pool disabled=no
```

### **Step 5: Configure Firewall Rules**

```bash
# Allow management system access
/ip firewall filter add chain=input protocol=tcp dst-port=8728 src-address=YOUR-MANAGEMENT-SERVER-IP action=accept comment="ISP Management API"

# Allow customer internet access
/ip firewall filter add chain=forward connection-state=established,related action=accept
/ip firewall filter add chain=forward connection-state=new out-interface=ether1 action=accept
/ip firewall nat add chain=srcnat out-interface=ether1 action=masquerade
```

## 🖥️ **Management System Configuration**

### **Step 1: Add MikroTik Device**

In the ISP Management System:

1. Navigate to **Network → Devices**
2. Click **Add Device**
3. Fill in device details:

```
Device Name: Router-Main-01
IP Address: ***********
Username: isp-manager
Password: SecurePassword123
API Port: 8728
Connection Type: API
Status: Active
Location: Main Office
```

### **Step 2: Test Connection**

```bash
# Test API connection from management system
docker-compose exec app php artisan mikrotik:test-connection ***********
```

### **Step 3: Configure IP Pools**

1. Navigate to **Network → IP Pools**
2. Create pools for different services:

```
PPPoE Pool:
- Name: PPPoE-Residential
- Network: *************/24
- Gateway: ***********
- Device: Router-Main-01
- Interface: pppoe-out1

Static IP Pool:
- Name: Static-Business
- Network: *************/24
- Gateway: *************
- Device: Router-Main-01
- Interface: ether3
```

## 👥 **Customer Service Provisioning**

### **PPPoE Service Creation**

When creating a PPPoE customer:

1. **System automatically**:
   - Creates PPPoE secret on MikroTik
   - Assigns bandwidth profile
   - Sets up queue rules
   - Configures user profile

2. **MikroTik commands executed**:
```bash
# Create PPPoE secret
/ppp secret add name=customer-username password=customer-password service=pppoe profile=customer-profile

# Create bandwidth profile
/ppp profile add name=customer-profile rate-limit=10M/2M local-address=*********** remote-address=**************

# Create queue rules
/queue simple add name=customer-username target=**************/32 max-limit=10M/2M
```

### **Static IP Service Creation**

When creating a static IP customer:

1. **System automatically**:
   - Reserves IP address
   - Creates DHCP lease
   - Sets up bandwidth limits
   - Configures firewall rules

2. **MikroTik commands executed**:
```bash
# Create DHCP lease
/ip dhcp-server lease add address=************** mac-address=aa:bb:cc:dd:ee:ff server=static-dhcp

# Create queue rule
/queue simple add name=customer-static-ip target=**************/32 max-limit=25M/5M
```

## 📊 **Monitoring and Management**

### **Real-time Monitoring**

The system monitors:
- Customer connection status
- Bandwidth usage
- Active sessions
- Device health

### **Automatic Actions**

#### **Service Suspension**
When a customer is suspended:
```bash
# Disable PPPoE user
/ppp secret disable customer-username

# Or disable queue
/queue simple disable customer-username
```

#### **Service Activation**
When payment is received:
```bash
# Enable PPPoE user
/ppp secret enable customer-username

# Or enable queue
/queue simple enable customer-username
```

#### **Bandwidth Changes**
When plan is upgraded:
```bash
# Update bandwidth profile
/ppp profile set customer-profile rate-limit=25M/5M

# Update queue
/queue simple set customer-username max-limit=25M/5M
```

## 🔧 **Advanced Configuration**

### **Multiple Router Setup**

For multiple MikroTik routers:

1. **Add each router** as separate device
2. **Configure load balancing**:
```bash
# Router 1 - PPPoE customers
/ppp profile set default remote-address=*************-***************

# Router 2 - Static IP customers  
/ip dhcp-server network set 0 address=*************/24
```

3. **Set up VLAN separation**:
```bash
# Create VLANs for different services
/interface vlan add name=vlan-pppoe vlan-id=100 interface=ether2
/interface vlan add name=vlan-static vlan-id=200 interface=ether3
```

### **Bandwidth Management**

#### **Burst Configuration**
```bash
# Allow burst speeds
/ppp profile set customer-profile rate-limit=10M/2M burst-limit=15M/3M burst-threshold=8M/1.5M burst-time=30s/30s
```

#### **Fair Queuing**
```bash
# Implement fair queuing
/queue type add name=pcq-download-fair kind=pcq pcq-rate=0 pcq-limit=50 pcq-classifier=dst-address
/queue type add name=pcq-upload-fair kind=pcq pcq-rate=0 pcq-limit=50 pcq-classifier=src-address

/queue simple add name=total-download parent=global queue=pcq-download-fair/pcq-upload-fair max-limit=100M/20M
```

### **Hotspot Integration**

For hotspot services:

```bash
# Create hotspot server
/ip hotspot setup

# Configure hotspot profile
/ip hotspot user profile add name=hotspot-1hour session-timeout=1h rate-limit=5M/1M

# Create hotspot users via API
/ip hotspot user add name=guest-user password=temp-pass profile=hotspot-1hour
```

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **API Connection Failed**
```bash
# Check API service status
/ip service print

# Verify user permissions
/user print detail

# Test from management system
telnet router-ip 8728
```

#### **Customer Can't Connect**
```bash
# Check PPPoE secret
/ppp secret print where name=customer-username

# Check active sessions
/ppp active print

# Verify interface status
/interface print stats
```

#### **Bandwidth Not Applied**
```bash
# Check queue rules
/queue simple print where name=customer-username

# Verify profile settings
/ppp profile print where name=customer-profile

# Check for conflicting rules
/queue simple print
```

### **Monitoring Commands**

```bash
# Monitor active PPPoE sessions
/ppp active print interval=1

# Check bandwidth usage
/queue simple print stats interval=1

# Monitor system resources
/system resource print

# Check logs
/log print where topics~"ppp"
```

## 📋 **Configuration Checklist**

### **MikroTik Setup**
- [ ] API service enabled
- [ ] Management user created
- [ ] PPPoE server configured
- [ ] DHCP server configured
- [ ] Firewall rules set
- [ ] Bandwidth profiles created

### **Management System**
- [ ] Device added and tested
- [ ] IP pools configured
- [ ] Bandwidth plans created
- [ ] Test customer created
- [ ] Service provisioning tested
- [ ] Monitoring verified

### **Security**
- [ ] API access restricted to management IP
- [ ] Strong passwords used
- [ ] Unnecessary services disabled
- [ ] Regular backups configured
- [ ] Firmware updated

## 🔒 **Security Best Practices**

1. **Use strong passwords** for API users
2. **Restrict API access** to management server IP only
3. **Enable API-SSL** for encrypted communication
4. **Regular firmware updates** for security patches
5. **Monitor logs** for suspicious activity
6. **Backup configurations** regularly

## 📞 **Support**

For MikroTik-specific issues:
- **MikroTik Documentation**: https://help.mikrotik.com/
- **MikroTik Forum**: https://forum.mikrotik.com/
- **System Integration Support**: Contact your ISP management system provider

---

**MikroTik Integration Complete!** 🌐

Your MikroTik routers are now fully integrated with the ISP Management System for automated customer provisioning and management.
