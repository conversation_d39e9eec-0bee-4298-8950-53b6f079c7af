#!/bin/bash

# ISP Management System - Rollback Script
# This script safely rolls back to a previous version

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[INFO]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_header() { echo -e "${BLUE}$1${NC}"; }

# Configuration
BACKUP_DIR="./backups/updates"
UPDATE_LOG="./storage/logs/updates.log"
VERSION_FILE="./VERSION"

# Logging function
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$UPDATE_LOG"
    echo "$1"
}

print_header "🔄 ISP Management System Rollback"
print_header "================================="

# Check if we're in the right directory
if [[ ! -f "artisan" ]] || [[ ! -f "composer.json" ]]; then
    print_error "This doesn't appear to be an ISP Management System directory"
    exit 1
fi

# Get rollback target
ROLLBACK_TARGET="$1"

if [[ -z "$ROLLBACK_TARGET" ]]; then
    print_status "Available rollback points:"
    git tag -l "rollback-*" | sort -r | head -10
    echo ""
    read -p "Enter rollback point (or 'latest' for most recent): " ROLLBACK_TARGET
    
    if [[ "$ROLLBACK_TARGET" == "latest" ]]; then
        ROLLBACK_TARGET=$(git tag -l "rollback-*" | sort -r | head -1)
    fi
fi

if [[ -z "$ROLLBACK_TARGET" ]]; then
    print_error "No rollback point specified"
    exit 1
fi

# Verify rollback point exists
if ! git tag -l | grep -q "^$ROLLBACK_TARGET$"; then
    print_error "Rollback point '$ROLLBACK_TARGET' not found"
    print_status "Available rollback points:"
    git tag -l "rollback-*" | sort -r
    exit 1
fi

# Get current version
CURRENT_VERSION="unknown"
if [[ -f "$VERSION_FILE" ]]; then
    CURRENT_VERSION=$(grep "Version:" "$VERSION_FILE" | cut -d' ' -f2 || echo "unknown")
fi

print_status "Current version: $CURRENT_VERSION"
print_status "Rolling back to: $ROLLBACK_TARGET"

# Find corresponding backup
ROLLBACK_DATE=$(echo "$ROLLBACK_TARGET" | sed 's/rollback-//')
BACKUP_PATH=""

# Look for backup with matching date
for backup in "$BACKUP_DIR"/backup-*; do
    if [[ -d "$backup" ]]; then
        backup_date=$(basename "$backup" | sed 's/backup-//')
        # Check if backup date is close to rollback date (within same day)
        if [[ "${backup_date:0:8}" == "${ROLLBACK_DATE:0:8}" ]]; then
            BACKUP_PATH="$backup"
            break
        fi
    fi
done

if [[ -z "$BACKUP_PATH" ]]; then
    print_warning "No matching backup found for $ROLLBACK_TARGET"
    print_status "Available backups:"
    ls -la "$BACKUP_DIR"/ 2>/dev/null || echo "No backups found"
    echo ""
    read -p "Continue with code rollback only? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Rollback cancelled"
        exit 0
    fi
else
    print_status "Found backup: $BACKUP_PATH"
fi

# Confirm rollback
echo ""
print_warning "⚠️  This will rollback your system to $ROLLBACK_TARGET"
print_warning "⚠️  Current data and customizations will be preserved"
echo ""
read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Rollback cancelled"
    exit 0
fi

# Put application in maintenance mode
print_status "Enabling maintenance mode..."
php artisan down --message="System rollback in progress" --retry=60 || true

# Create backup of current state before rollback
print_status "Creating pre-rollback backup..."
PRE_ROLLBACK_BACKUP="$BACKUP_DIR/pre-rollback-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$PRE_ROLLBACK_BACKUP"
cp -r .env "$PRE_ROLLBACK_BACKUP/" 2>/dev/null || true
cp -r storage/ "$PRE_ROLLBACK_BACKUP/" 2>/dev/null || true
cp -r custom/ "$PRE_ROLLBACK_BACKUP/" 2>/dev/null || true

# Backup current database
if command -v docker-compose &> /dev/null && docker-compose ps postgres | grep -q "Up"; then
    docker-compose exec -T postgres pg_dump -U isp_user isp_management > "$PRE_ROLLBACK_BACKUP/database.sql"
fi

# Stash any uncommitted changes
print_status "Stashing current changes..."
git stash push -m "Pre-rollback stash $(date)" || true

# Perform git rollback
print_status "Rolling back code..."
git checkout "$ROLLBACK_TARGET"

# Restore files from backup if available
if [[ -n "$BACKUP_PATH" && -d "$BACKUP_PATH" ]]; then
    print_status "Restoring files from backup..."
    
    # Restore environment file
    if [[ -f "$BACKUP_PATH/.env" ]]; then
        cp "$BACKUP_PATH/.env" ./
        print_status "✅ Environment file restored"
    fi
    
    # Restore storage (but preserve current logs)
    if [[ -d "$BACKUP_PATH/storage" ]]; then
        # Backup current logs
        mkdir -p /tmp/current-logs
        cp -r storage/logs/* /tmp/current-logs/ 2>/dev/null || true
        
        # Restore storage
        cp -r "$BACKUP_PATH/storage/"* storage/ 2>/dev/null || true
        
        # Restore current logs
        cp -r /tmp/current-logs/* storage/logs/ 2>/dev/null || true
        rm -rf /tmp/current-logs
        
        print_status "✅ Storage files restored"
    fi
    
    # Restore custom files
    if [[ -d "$BACKUP_PATH/custom" ]]; then
        cp -r "$BACKUP_PATH/custom/"* custom/ 2>/dev/null || true
        print_status "✅ Custom files restored"
    fi
    
    # Ask about database restore
    if [[ -f "$BACKUP_PATH/database.sql" ]]; then
        echo ""
        print_warning "Database backup found from rollback point"
        read -p "Do you want to restore the database? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "Restoring database..."
            if command -v docker-compose &> /dev/null; then
                docker-compose exec -T postgres psql -U isp_user -d isp_management < "$BACKUP_PATH/database.sql"
                print_status "✅ Database restored"
            else
                print_warning "Docker not available - manual database restore required"
            fi
        else
            print_status "Database not restored - keeping current data"
        fi
    fi
fi

# Update dependencies
print_status "Updating dependencies..."
if command -v docker-compose &> /dev/null; then
    docker-compose run --rm app composer install --no-dev --optimize-autoloader
else
    composer install --no-dev --optimize-autoloader
fi

# Run migrations (in case we need to rollback database schema)
print_status "Checking database migrations..."
if command -v docker-compose &> /dev/null; then
    docker-compose run --rm app php artisan migrate:status
else
    php artisan migrate:status
fi

# Clear caches
print_status "Clearing caches..."
if command -v docker-compose &> /dev/null; then
    docker-compose run --rm app php artisan config:clear
    docker-compose run --rm app php artisan cache:clear
    docker-compose run --rm app php artisan route:clear
    docker-compose run --rm app php artisan view:clear
else
    php artisan config:clear
    php artisan cache:clear
    php artisan route:clear
    php artisan view:clear
fi

# Update version file
ROLLBACK_VERSION=$(git describe --tags --exact-match HEAD 2>/dev/null || git rev-parse --short HEAD)
cat > "$VERSION_FILE" << EOF
ISP Management System
Version: $ROLLBACK_VERSION
Rollback Date: $(date)
Rolled Back From: $CURRENT_VERSION
Rollback Point: $ROLLBACK_TARGET
Pre-Rollback Backup: $PRE_ROLLBACK_BACKUP
EOF

# Restart services
print_status "Restarting services..."
if command -v docker-compose &> /dev/null; then
    docker-compose restart app queue scheduler
fi

# Disable maintenance mode
print_status "Disabling maintenance mode..."
php artisan up

log_message "Rollback completed: $CURRENT_VERSION -> $ROLLBACK_VERSION (point: $ROLLBACK_TARGET)"

echo ""
print_status "🎉 Rollback completed successfully!"
print_status "✅ Rolled back to: $ROLLBACK_TARGET"
print_status "📁 Pre-rollback backup: $PRE_ROLLBACK_BACKUP"
echo ""
print_status "📋 Post-Rollback Checklist:"
print_status "  1. Test your system functionality"
print_status "  2. Verify data integrity"
print_status "  3. Check customizations"
print_status "  4. Test MikroTik connections"
echo ""
print_warning "💡 If you need to go forward again, you can run ./update.sh"
echo ""
